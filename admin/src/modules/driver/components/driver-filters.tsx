'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, X, MapPin, Mail, Phone, Hash } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { useAllCities } from '@/modules/city/api/queries';
import { useListVehicleCategory } from '@/modules/vehicle-category/api/queries';

export interface DriverFiltersProps {
   onSearchChange: (search: string) => void;
   onCityChange: (cityId: string | undefined) => void;
   onNameChange: (name: string | undefined) => void;
   onEmailChange: (email: string | undefined) => void;
   onPhoneChange: (phone: string | undefined) => void;
   onStatusChange: (status: string | undefined) => void;
   onVehicleTypeChange: (vehicleTypeId: string | undefined) => void;
   onDriverIdChange: (driverId: string | undefined) => void;
   search: string;
   cityId: string | undefined;
   name: string | undefined;
   email: string | undefined;
   phoneNumber: string | undefined;
   status: string | undefined;
   vehicleTypeId: string | undefined;
   driverId: string | undefined;
}

export function DriverFilters({
   onSearchChange,
   onCityChange,
   onNameChange,
   onEmailChange,
   onPhoneChange,
   onStatusChange,
   onVehicleTypeChange,
   onDriverIdChange,
   search,
   cityId,
   name,
   email,
   phoneNumber,
   status,
   vehicleTypeId,
   driverId,
   isLoading,
}: DriverFiltersProps & { isLoading?: boolean }) {
   const [searchValue, setSearchValue] = useState(search || '');
   const [emailValue, setEmailValue] = useState(email || '');
   const [phoneValue, setPhoneValue] = useState(phoneNumber || '');
   const [driverIdValue, setDriverIdValue] = useState(driverId || '');
   const [isSearching, setIsSearching] = useState(false);
   const [isEmailSearching, setIsEmailSearching] = useState(false);
   const [isPhoneSearching, setIsPhoneSearching] = useState(false);
   const [isDriverIdSearching, setIsDriverIdSearching] = useState(false);
   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   const emailTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   const phoneTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   const driverIdTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Fetch cities and vehicle types for the dropdowns
   const citiesQuery = useAllCities();
   const vehicleTypesQuery = useListVehicleCategory({});

   // Update local search state when props change
   useEffect(() => {
      setSearchValue(search || '');
   }, [search]);

   useEffect(() => {
      setEmailValue(email || '');
   }, [email]);

   useEffect(() => {
      setPhoneValue(phoneNumber || '');
   }, [phoneNumber]);

   useEffect(() => {
      setDriverIdValue(driverId || '');
   }, [driverId]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
         if (emailTimeoutRef.current) {
            clearTimeout(emailTimeoutRef.current);
         }
         if (phoneTimeoutRef.current) {
            clearTimeout(phoneTimeoutRef.current);
         }
         if (driverIdTimeoutRef.current) {
            clearTimeout(driverIdTimeoutRef.current);
         }
      };
   }, []);

   // Handle search input with debounce
   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);

      // Show searching indicator
      setIsSearching(true);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      searchTimeoutRef.current = setTimeout(() => {
         onNameChange(value);
         searchTimeoutRef.current = null;
         setIsSearching(false);
      }, 500); // 500ms debounce time
   };

   // Handle email input with debounce
   const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setEmailValue(value);

      // Show searching indicator
      setIsEmailSearching(true);

      // Clear any existing timeout
      if (emailTimeoutRef.current) {
         clearTimeout(emailTimeoutRef.current);
      }

      // Set a new timeout
      emailTimeoutRef.current = setTimeout(() => {
         onEmailChange(value);
         emailTimeoutRef.current = null;
         setIsEmailSearching(false);
      }, 500); // 500ms debounce time
   };

   // Handle phone input with debounce
   const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPhoneValue(value);

      // Show searching indicator
      setIsPhoneSearching(true);

      // Clear any existing timeout
      if (phoneTimeoutRef.current) {
         clearTimeout(phoneTimeoutRef.current);
      }

      // Set a new timeout
      phoneTimeoutRef.current = setTimeout(() => {
         onPhoneChange(value);
         phoneTimeoutRef.current = null;
         setIsPhoneSearching(false);
      }, 500); // 500ms debounce time
   };

   // Handle driver ID input with debounce
   const handleDriverIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setDriverIdValue(value);

      // Show searching indicator
      setIsDriverIdSearching(true);

      // Clear any existing timeout
      if (driverIdTimeoutRef.current) {
         clearTimeout(driverIdTimeoutRef.current);
      }

      // Set a new timeout
      driverIdTimeoutRef.current = setTimeout(() => {
         onDriverIdChange(value);
         driverIdTimeoutRef.current = null;
         setIsDriverIdSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear all filters
   const handleClearFilters = () => {
      // Clear any pending timeouts
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
         searchTimeoutRef.current = null;
      }
      if (emailTimeoutRef.current) {
         clearTimeout(emailTimeoutRef.current);
         emailTimeoutRef.current = null;
      }
      if (phoneTimeoutRef.current) {
         clearTimeout(phoneTimeoutRef.current);
         phoneTimeoutRef.current = null;
      }
      if (driverIdTimeoutRef.current) {
         clearTimeout(driverIdTimeoutRef.current);
         driverIdTimeoutRef.current = null;
      }

      setIsSearching(false);
      setIsEmailSearching(false);
      setIsPhoneSearching(false);
      setIsDriverIdSearching(false);
      setSearchValue('');
      setEmailValue('');
      setPhoneValue('');
      setDriverIdValue('');
      onSearchChange('');
      onCityChange(undefined);
      onNameChange(undefined);
      onEmailChange(undefined);
      onPhoneChange(undefined);
      onStatusChange(undefined);
      onVehicleTypeChange(undefined);
      onDriverIdChange(undefined);
   };

   // Check if any filters are active
   const hasActiveFilters = !!search || !!cityId || !!name || !!email || !!phoneNumber || !!status || !!vehicleTypeId || !!driverId;

   return (
      <div className='flex flex-col space-y-4 mb-4'>
         <div className='flex justify-between items-center'>
            {/* Left container - Search fields */}
            <div className='flex gap-2 items-center'>
               {/* Driver Name Search */}
               <div className='relative w-[160px]'>
                  <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by name...'
                     value={searchValue}
                     onChange={handleSearchChange}
                     className='pl-8'
                  />
                  {(isSearching || (isLoading && searchValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {searchValue && !isSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (searchTimeoutRef.current) {
                              clearTimeout(searchTimeoutRef.current);
                              searchTimeoutRef.current = null;
                           }
                           setIsSearching(false);
                           setSearchValue('');
                           onNameChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>

               {/* Email Search */}
               <div className='relative w-[170px]'>
                  <Mail className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by email...'
                     value={emailValue}
                     onChange={handleEmailChange}
                     className='pl-8'
                  />
                  {(isEmailSearching || (isLoading && emailValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {emailValue && !isEmailSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (emailTimeoutRef.current) {
                              clearTimeout(emailTimeoutRef.current);
                              emailTimeoutRef.current = null;
                           }
                           setIsEmailSearching(false);
                           setEmailValue('');
                           onEmailChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>

               {/* Phone Search */}
               <div className='relative w-[170px]'>
                  <Phone className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by phone...'
                     value={phoneValue}
                     onChange={handlePhoneChange}
                     className='pl-8'
                  />
                  {(isPhoneSearching || (isLoading && phoneValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {phoneValue && !isPhoneSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (phoneTimeoutRef.current) {
                              clearTimeout(phoneTimeoutRef.current);
                              phoneTimeoutRef.current = null;
                           }
                           setIsPhoneSearching(false);
                           setPhoneValue('');
                           onPhoneChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>

               {/* Driver ID Search */}
               <div className='relative w-[170px]'>
                  <Hash className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by driver ID...'
                     value={driverIdValue}
                     onChange={handleDriverIdChange}
                     className='pl-8'
                  />
                  {(isDriverIdSearching || (isLoading && driverIdValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {driverIdValue && !isDriverIdSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (driverIdTimeoutRef.current) {
                              clearTimeout(driverIdTimeoutRef.current);
                              driverIdTimeoutRef.current = null;
                           }
                           setIsDriverIdSearching(false);
                           setDriverIdValue('');
                           onDriverIdChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>
            </div>

            {/* Right container - Status, Vehicle Type, City filters and Clear button */}
            <div className='flex gap-2 items-center'>
               {/* Status Filter */}
               <Select
                  value={status || 'all'}
                  onValueChange={value => onStatusChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[100px] cursor-pointer'>
                     <SelectValue placeholder='All Status' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Status</SelectItem>
                     <SelectItem value='active'>Active</SelectItem>
                     <SelectItem value='inactive'>Inactive</SelectItem>
                     <SelectItem value='disabled'>Disabled</SelectItem>
                     <SelectItem value='pending'>Pending</SelectItem>
                  </SelectContent>
               </Select>

               {/* Vehicle Type Filter */}
               <Select
                  value={vehicleTypeId || 'all'}
                  onValueChange={value => onVehicleTypeChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[140px] cursor-pointer'>
                     <SelectValue placeholder='All Vehicle Types' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Vehicle Types</SelectItem>
                     {vehicleTypesQuery.data?.data?.map(vehicleType => (
                        <SelectItem key={vehicleType.id} value={vehicleType.id}>
                           {vehicleType.name}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>

               {/* City Filter */}
               <Select
                  value={cityId || 'all'}
                  onValueChange={value => onCityChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[120px] cursor-pointer'>
                     <MapPin className='h-4 w-4 mr-1' />
                     <SelectValue placeholder='All Cities' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Cities</SelectItem>
                     {citiesQuery.data?.data?.map(city => (
                        <SelectItem key={city.id} value={city.id}>
                           {city.name}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>
            </div>
         </div>

         {/* Clear Filters Button - Below the filters */}
         {hasActiveFilters && (
            <div className='flex justify-end'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={handleClearFilters}
                  className='text-gray-700 border-gray-300'
               >
                  Clear Filters
               </Button>
            </div>
         )}
      </div>
   );
}
