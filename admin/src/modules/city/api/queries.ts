import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   City,
   CityResponse,
   ListCityParams,
   ListCityResponse,
   NearbyCitiesParams,
   NearbyCitiesResponse,
} from '../types/city';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListCities = ({ page = 1, limit = 10, search, state, status, cityId }: ListCityParams) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['cities', page, limit, search, state, status, cityId],
      refetchOnWindowFocus: false,
      enabled: hasPermission(RBAC_PERMISSIONS.CITY.LIST),
      queryFn: (): Promise<ListCityResponse> => {
         return apiClient.get('/cities/admin/paginate', {
            params: {
               page,
               limit,
               search,
               state,
               status,
               cityId,
            },
         });
      },
   });
};

export const useGetCity = (id: string | null) => {
   return useQuery({
      queryKey: ['city', id],
      queryFn: (): Promise<CityResponse> => {
         return apiClient.get(`/cities/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for fetching all cities (for dropdown usage)
 */
export const useAllCities = () => {
   return useQuery({
      queryKey: ['cities-all'],
      queryFn: (): Promise<{
         success: boolean;
         message: string;
         data: City[];
         timestamp: number;
      }> => {
         return apiClient.get('/cities');
      },
      staleTime: 5 * 60 * 1000, // 5 minutes - cities don't change often
      refetchOnWindowFocus: false,
   });
};

/**
 * Hook for fetching nearby cities by geo coordinates
 */
export const useNearbyCities = ({ lat, lng, radiusInMeter }: NearbyCitiesParams = {}) => {
   return useQuery({
      queryKey: ['cities-nearby', lat, lng, radiusInMeter],
      queryFn: (): Promise<NearbyCitiesResponse> => {
         return apiClient.get('/cities/nearby', {
            // params: {
            //    lat,
            //    lng,
            //    radiusInMeter,
            // },
         });
      },
   });
};

/**
 * Hook for fetching zone types by city ID
 * Returns only zone types that exist in the city's zones
 */
export const useCityZoneTypes = (cityId: string | null) => {
   return useQuery({
      queryKey: ['city-zone-types', cityId],
      queryFn: (): Promise<{
         success: boolean;
         message: string;
         data: Array<{
            id: string;
            name: string;
            description?: string | null;
            algorithm: string;
            config?: any;
            isActive: boolean;
            createdAt: string;
            updatedAt: string;
            deletedAt?: string | null;
         }>;
         timestamp: number;
      }> => {
         return apiClient.get(`/cities/${cityId}/zone-types`);
      },
      enabled: !!cityId,
      refetchOnWindowFocus: false,
   });
};
