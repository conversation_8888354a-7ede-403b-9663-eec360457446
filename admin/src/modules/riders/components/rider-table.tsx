'use client';

import { CustomPagination } from '@/components/pagination';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { Rider, RiderListResponse } from '../types/rider';
import { RiderTableEmpty } from './rider-table-empty';
import { RiderTableLoading } from './rider-table-loading';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { Copy } from 'lucide-react';
import { toast } from '@/lib/toast';

// Define the columns for the table
const getColumns = ({
   handleViewClick,
   withPermission,
}: {
   handleViewClick: (id: string) => void;
   withPermission: any;
}): ColumnDef<Rider>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Rider</div>,
      cell: ({ row }) => {
         const rider = row.original as Rider;
         const fullName = `${rider.firstName || ''} ${rider.lastName || ''}`.trim() || 'No Name';

         const handleCopyId = async (e: React.MouseEvent) => {
            e.stopPropagation();
            try {
               await navigator.clipboard.writeText(rider.id);
               toast.success('ID copied');
            } catch {
               toast.error('Failed to copy ID');
            }
         };

         return (
            <div className='text-left'>
               <div className='font-semibold text-sm'>{fullName}</div>
               <div className='flex items-center gap-1 text-xs text-gray-500'>
                  <span>ID: {rider.id.slice(0, 8)}...</span>
                  <button
                     onClick={handleCopyId}
                     className='hover:text-gray-700 transition-colors p-0.5 hover:bg-gray-100 rounded cursor-pointer'
                     title='Copy full ID'
                  >
                     <Copy className='w-3 h-3' />
                  </button>
               </div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'contact',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Contact</div>,
      cell: ({ row }) => {
         const rider = row.original as Rider;
         return (
            <div className='text-left'>
               <div className='text-sm'>{rider.user.phoneNumber}</div>
               <div className='text-xs text-gray-500'>{rider.user.email || '-'}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const rider = row.original as Rider;

         // Status color mapping
         const getStatusColor = (status: string) => {
            switch (status) {
               case 'active':
                  return 'bg-green-100 text-green-800';
               case 'inactive':
                  return 'bg-gray-100 text-gray-800';
               case 'disabled':
                  return 'bg-red-100 text-red-800';
               case 'pending':
                  return 'bg-yellow-100 text-yellow-800';
               case 'invited':
                  return 'bg-blue-100 text-blue-800';
               default:
                  return 'bg-gray-100 text-gray-800';
            }
         };

         const formatStatus = (status: string) => {
            return status.charAt(0).toUpperCase() + status.slice(1);
         };

         return (
            <div className='flex justify-center'>
               <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                     rider.status
                  )}`}
               >
                  {formatStatus(rider.status)}
               </span>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'createdAt',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Joined Date</div>
      ),
      cell: ({ row }) => {
         const rider = row.original as Rider;
         const date = new Date(rider.createdAt);
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {date.toLocaleDateString('en-US', {
                     year: 'numeric',
                     month: 'short',
                     day: 'numeric',
                  })}
               </div>
            </div>
         );
      },
      size: 120,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const rider = row.original as Rider;

         return (
            <div className='flex justify-center'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.RIDER.VIEW, () => handleViewClick(rider.id));
                  }}
               >
                  View Details
               </button>
            </div>
         );
      },
      size: 150,
   },
];

// Table component props
interface RiderTableProps {
   data: RiderListResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters?: boolean;
   onClearFilters?: () => void;
}

export function RiderTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   onClearFilters,
}: RiderTableProps) {
   const router = useRouter();
   const { withPermission } = useRoleBasedAccess();

   const handleViewClick = (id: string) => {
      router.push(`/dashboard/riders/${id}`);
   };

   const columns = getColumns({
      handleViewClick,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <RiderTableLoading />;
   }

   if (!data?.data?.length) {
      // Show different empty state based on filters
      if (hasFilters) {
         return (
            <div className='flex flex-col items-center justify-center py-12 px-4'>
               <div className='rounded-full bg-gray-100 p-4 mb-4'>
                  <svg
                     xmlns='http://www.w3.org/2000/svg'
                     className='h-8 w-8 text-gray-400'
                     fill='none'
                     viewBox='0 0 24 24'
                     stroke='currentColor'
                  >
                     <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                     />
                  </svg>
               </div>
               <h3 className='text-lg font-semibold text-gray-900 mb-1'>No riders found</h3>
               <p className='text-sm text-gray-500 text-center max-w-sm mb-4'>
                  No riders match your current filters. Try adjusting your search criteria.
               </p>
               {onClearFilters && (
                  <button
                     onClick={onClearFilters}
                     className='text-sm font-medium text-blue-600 hover:text-blue-700'
                  >
                     Clear filters
                  </button>
               )}
            </div>
         );
      }

      return <RiderTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}
      </div>
   );
}
