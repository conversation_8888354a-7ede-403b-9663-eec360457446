'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, X, Mail, Phone, Hash } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
// import { useAllCities } from '@/modules/city/api/queries';

export interface RiderFiltersProps {
  onNameChange: (name: string | undefined) => void;
  onEmailChange: (email: string | undefined) => void;
  onPhoneChange: (phone: string | undefined) => void;
  onCityChange: (cityId: string | undefined) => void;
  onStatusChange: (status: string | undefined) => void;
  onRiderIdChange: (riderId: string | undefined) => void;
  name: string | undefined;
  email: string | undefined;
  phoneNumber: string | undefined;
  cityId: string | undefined;
  status: string | undefined;
  riderId: string | undefined;
}

export function RiderFilters({
  onNameChange,
  onEmailChange,
  onPhoneChange,
  onCityChange,
  onStatusChange,
  onRiderIdChange,
  name,
  email,
  phoneNumber,
  cityId,
  status,
  riderId,
  isLoading,
}: RiderFiltersProps & { isLoading?: boolean }) {
   const [nameValue, setNameValue] = useState(name || '');
   const [emailValue, setEmailValue] = useState(email || '');
   const [phoneValue, setPhoneValue] = useState(phoneNumber || '');
   const [riderIdValue, setRiderIdValue] = useState(riderId || '');
   const [isNameSearching, setIsNameSearching] = useState(false);
   const [isEmailSearching, setIsEmailSearching] = useState(false);
   const [isPhoneSearching, setIsPhoneSearching] = useState(false);
   const [isRiderIdSearching, setIsRiderIdSearching] = useState(false);
   const nameTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   const emailTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   const phoneTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   const riderIdTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Fetch cities for the dropdown
   //   const citiesQuery = useAllCities();

   // Update local search state when props change
   useEffect(() => {
      setNameValue(name || '');
   }, [name]);

   useEffect(() => {
      setEmailValue(email || '');
   }, [email]);

   useEffect(() => {
      setPhoneValue(phoneNumber || '');
   }, [phoneNumber]);

   useEffect(() => {
      setRiderIdValue(riderId || '');
   }, [riderId]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (nameTimeoutRef.current) {
            clearTimeout(nameTimeoutRef.current);
         }
         if (emailTimeoutRef.current) {
            clearTimeout(emailTimeoutRef.current);
         }
         if (phoneTimeoutRef.current) {
            clearTimeout(phoneTimeoutRef.current);
         }
         if (riderIdTimeoutRef.current) {
            clearTimeout(riderIdTimeoutRef.current);
         }
      };
   }, []);

   // Handle name input with debounce
   const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setNameValue(value);

      // Show searching indicator
      setIsNameSearching(true);

      // Clear any existing timeout
      if (nameTimeoutRef.current) {
         clearTimeout(nameTimeoutRef.current);
      }

      // Set a new timeout
      nameTimeoutRef.current = setTimeout(() => {
         onNameChange(value);
         nameTimeoutRef.current = null;
         setIsNameSearching(false);
      }, 500); // 500ms debounce time
   };

   // Handle email input with debounce
   const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setEmailValue(value);

      // Show searching indicator
      setIsEmailSearching(true);

      // Clear any existing timeout
      if (emailTimeoutRef.current) {
         clearTimeout(emailTimeoutRef.current);
      }

      // Set a new timeout
      emailTimeoutRef.current = setTimeout(() => {
         onEmailChange(value);
         emailTimeoutRef.current = null;
         setIsEmailSearching(false);
      }, 500); // 500ms debounce time
   };

   // Handle phone input with debounce
   const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPhoneValue(value);

      // Show searching indicator
      setIsPhoneSearching(true);

      // Clear any existing timeout
      if (phoneTimeoutRef.current) {
         clearTimeout(phoneTimeoutRef.current);
      }

      // Set a new timeout
      phoneTimeoutRef.current = setTimeout(() => {
         onPhoneChange(value);
         phoneTimeoutRef.current = null;
         setIsPhoneSearching(false);
      }, 500); // 500ms debounce time
   };

   // Handle rider ID input with debounce
   const handleRiderIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setRiderIdValue(value);

      // Show searching indicator
      setIsRiderIdSearching(true);

      // Clear any existing timeout
      if (riderIdTimeoutRef.current) {
         clearTimeout(riderIdTimeoutRef.current);
      }

      // Set a new timeout
      riderIdTimeoutRef.current = setTimeout(() => {
         onRiderIdChange(value);
         riderIdTimeoutRef.current = null;
         setIsRiderIdSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear all filters
   const handleClearFilters = () => {
      // Clear any pending timeouts
      if (nameTimeoutRef.current) {
         clearTimeout(nameTimeoutRef.current);
         nameTimeoutRef.current = null;
      }
      if (emailTimeoutRef.current) {
         clearTimeout(emailTimeoutRef.current);
         emailTimeoutRef.current = null;
      }
      if (phoneTimeoutRef.current) {
         clearTimeout(phoneTimeoutRef.current);
         phoneTimeoutRef.current = null;
      }
      if (riderIdTimeoutRef.current) {
         clearTimeout(riderIdTimeoutRef.current);
         riderIdTimeoutRef.current = null;
      }

      setIsNameSearching(false);
      setIsEmailSearching(false);
      setIsPhoneSearching(false);
      setIsRiderIdSearching(false);
      setNameValue('');
      setEmailValue('');
      setPhoneValue('');
      setRiderIdValue('');
      onNameChange(undefined);
      onEmailChange(undefined);
      onPhoneChange(undefined);
      onCityChange(undefined);
      onStatusChange(undefined);
      onRiderIdChange(undefined);
   };

   // Check if any filters are active
   const hasActiveFilters = !!name || !!email || !!phoneNumber || !!cityId || !!status || !!riderId;

   return (
      <div className='flex flex-col space-y-4 mb-4'>
         <div className='flex justify-between items-center'>
            {/* Left container - Search fields */}
            <div className='flex gap-2 items-center'>
               {/* Rider Name Search */}
               <div className='relative w-[160px]'>
                  <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by name...'
                     value={nameValue}
                     onChange={handleNameChange}
                     className='pl-8'
                  />
                  {(isNameSearching || (isLoading && nameValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {nameValue && !isNameSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (nameTimeoutRef.current) {
                              clearTimeout(nameTimeoutRef.current);
                              nameTimeoutRef.current = null;
                           }
                           setIsNameSearching(false);
                           setNameValue('');
                           onNameChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>

               {/* Email Search */}
               <div className='relative w-[170px]'>
                  <Mail className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by email...'
                     value={emailValue}
                     onChange={handleEmailChange}
                     className='pl-8'
                  />
                  {(isEmailSearching || (isLoading && emailValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {emailValue && !isEmailSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (emailTimeoutRef.current) {
                              clearTimeout(emailTimeoutRef.current);
                              emailTimeoutRef.current = null;
                           }
                           setIsEmailSearching(false);
                           setEmailValue('');
                           onEmailChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>

               {/* Phone Search */}
               <div className='relative w-[170px]'>
                  <Phone className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by phone...'
                     value={phoneValue}
                     onChange={handlePhoneChange}
                     className='pl-8'
                  />
                  {(isPhoneSearching || (isLoading && phoneValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {phoneValue && !isPhoneSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (phoneTimeoutRef.current) {
                              clearTimeout(phoneTimeoutRef.current);
                              phoneTimeoutRef.current = null;
                           }
                           setIsPhoneSearching(false);
                           setPhoneValue('');
                           onPhoneChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>

               {/* Rider ID Search */}
               <div className='relative w-[170px]'>
                  <Hash className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search by rider ID...'
                     value={riderIdValue}
                     onChange={handleRiderIdChange}
                     className='pl-8'
                  />
                  {(isRiderIdSearching || (isLoading && riderIdValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {riderIdValue && !isRiderIdSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (riderIdTimeoutRef.current) {
                              clearTimeout(riderIdTimeoutRef.current);
                              riderIdTimeoutRef.current = null;
                           }
                           setIsRiderIdSearching(false);
                           setRiderIdValue('');
                           onRiderIdChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>
            </div>

            {/* Right container - Status and City filters */}
            <div className='flex gap-2 items-center'>
               {/* Status Filter */}
               <Select
                  value={status || 'all'}
                  onValueChange={value => onStatusChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[100px] cursor-pointer'>
                     <SelectValue placeholder='All Status' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Status</SelectItem>
                     <SelectItem value='active'>Active</SelectItem>
                     <SelectItem value='inactive'>Inactive</SelectItem>
                     <SelectItem value='disabled'>Disabled</SelectItem>
                     <SelectItem value='pending'>Pending</SelectItem>
                     <SelectItem value='invited'>Invited</SelectItem>
                  </SelectContent>
               </Select>

               {/* City Filter */}
               {/* <Select
            value={cityId || 'all'}
            onValueChange={value => onCityChange(value === 'all' ? undefined : value)}
          >
            <SelectTrigger className='w-auto min-w-[120px] cursor-pointer'>
              <MapPin className='h-4 w-4 mr-1' />
              <SelectValue placeholder='All Cities' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Cities</SelectItem>
              {citiesQuery.data?.data?.map(city => (
                <SelectItem key={city.id} value={city.id}>
                  {city.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select> */}
            </div>
         </div>

         {/* Clear Filters Button - Below the filters */}
         {hasActiveFilters && (
            <div className='flex justify-end'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={handleClearFilters}
                  className='text-gray-700 border-gray-300'
               >
                  Clear Filters
               </Button>
            </div>
         )}
      </div>
   );
}
