'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Rider } from '../types/rider';
import { formatDateWithMonth } from '@/lib/date-utils';
import { Edit, Copy } from 'lucide-react';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { toast } from '@/lib/toast';

interface RiderPersonalDetailsProps {
  rider: Rider;
  onEdit?: () => void;
}

export function RiderPersonalDetails({ rider, onEdit }: RiderPersonalDetailsProps) {
  const { withPermission } = useRoleBasedAccess();

  const handleCopyId = async () => {
    try {
      await navigator.clipboard.writeText(rider.id);
      toast.success('ID copied');
    } catch {
      toast.error('Failed to copy ID');
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header with Edit Button */}
      <div className='flex justify-between items-center'>
        <h3 className='text-lg font-semibold text-gray-900'>Personal Information</h3>
        {onEdit && (
          <Button
            size='sm'
            variant='outline'
            onClick={() => withPermission(RBAC_PERMISSIONS.RIDER.EDIT, onEdit)}
            className='gap-2'
          >
            <Edit className='h-4 w-4' />
            Edit Profile
          </Button>
        )}
      </div>

      {/* Main 2-column grid for basic and contact info */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {/* Left Column - Basic Information */}
        <div className='space-y-4'>
          <h4 className='text-base font-semibold text-gray-700'>Basic Information</h4>
          <div className='space-y-3'>
            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600'>Rider ID</span>
              <div className='flex items-center gap-2'>
                <span className='text-sm text-gray-900 font-mono'>{rider.id}</span>
                <button
                  onClick={handleCopyId}
                  className='text-gray-500 hover:text-gray-700 transition-colors p-1 hover:bg-gray-200 rounded cursor-pointer'
                  title='Copy ID'
                >
                  <Copy className='w-4 h-4' />
                </button>
              </div>
            </div>
            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600'>Full Name</span>
              <span className='text-sm text-gray-900'>
                {`${rider.firstName || ''} ${rider.lastName || ''}`.trim() || 'Not provided'}
              </span>
            </div>
            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600'>Gender</span>
              <span className='text-sm text-gray-900'>
                {rider.gender
                  ? rider.gender.charAt(0).toUpperCase() + rider.gender.slice(1).toLowerCase()
                  : 'Not provided'}
              </span>
            </div>
            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600'>Date of Birth</span>
              <span className='text-sm text-gray-900'>
                {rider.dob ? formatDateWithMonth(rider.dob) : 'Not provided'}
              </span>
            </div>
          </div>
        </div>

        {/* Right Column - Contact Information */}
        <div className='space-y-4'>
          <h4 className='text-base font-semibold text-gray-700'>Contact Information</h4>
          <div className='space-y-3'>
            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600'>Phone Number</span>
              <div className='flex items-center gap-2'>
                <span className='text-sm text-gray-900'>{rider.user.phoneNumber}</span>
                <Badge
                  className={`text-xs ${
                    rider.user.phoneVerifiedAt
                      ? 'bg-green-100 text-green-700'
                      : 'bg-gray-100 text-gray-700'
                  }`}
                >
                  {rider.user.phoneVerifiedAt ? 'Verified' : 'Unverified'}
                </Badge>
              </div>
            </div>

            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600 flex-shrink-0'>Email Address</span>
              <div className='flex items-center gap-2 min-w-0'>
                <span className='text-sm text-gray-900 truncate'>
                  {rider.user.email || 'Not provided'}
                </span>
                {rider.user.email && (
                  <Badge
                    className={`text-xs flex-shrink-0 ${
                      rider.user.emailVerifiedAt
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    {rider.user.emailVerifiedAt ? 'Verified' : 'Unverified'}
                  </Badge>
                )}
              </div>
            </div>

            <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
              <span className='text-sm font-medium text-gray-600'>Status</span>
              <Badge
                className={`text-xs ${
                  rider.status === 'active'
                    ? 'bg-green-100 text-green-700'
                    : rider.status === 'disabled'
                      ? 'bg-red-100 text-red-700'
                      : rider.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-700'
                        : 'bg-gray-100 text-gray-700'
                }`}
              >
                {rider.status.charAt(0).toUpperCase() + rider.status.slice(1)}
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
