'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { DatePicker } from '@/modules/coupon/components/date-picker';
import { useListRiders } from '@/modules/riders/api/queries';
import { Rider } from '@/modules/riders/types/rider';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { zodResolver } from '@hookform/resolvers/zod';
import { addMinutes, formatISO, isAfter, set, startOfDay } from 'date-fns';
import { Check, ChevronsUpDown, Plus, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import * as z from 'zod';
import { useCreateRide } from '../api/mutations';
import { useSearchRides } from '../api/queries';
import { LocationSearch } from '../components/location-search';
import { Location, RideSearchResult } from '../types/book-ride';

type RiderType = 'existing' | 'new';
type PickupType = 'now' | 'later';

// Zod schema for form validation
const bookRideSchema = z
   .object({
      riderType: z.enum(['existing', 'new']),
      selectedRider: z.custom<Rider | null>(),
      newRiderName: z.string().optional(),
      newRiderPhone: z.string().optional(),
      newRiderEmail: z.string().email().optional().or(z.literal('')),
      pickupLocation: z.custom<Location | null>(),
      destinationLocation: z.custom<Location | null>(),
      stops: z.array(z.custom<{ location: Location | null; input: string }>()),
      selectedProductId: z.string().min(1, 'Please select a product'),
      pickupType: z.enum(['now', 'later']),
      pickupDate: z.date().optional(),
      pickupTime: z.string().optional(),
   })
   .superRefine((data, ctx) => {
      // Validate rider selection
      if (data.riderType === 'existing' && !data.selectedRider) {
         ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Please select an existing rider',
            path: ['selectedRider'],
         });
      }

      if (data.riderType === 'new') {
         if (!data.newRiderName || data.newRiderName.trim() === '') {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Rider name is required',
               path: ['newRiderName'],
            });
         }
         if (!data.newRiderPhone || data.newRiderPhone.trim() === '') {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Phone number is required',
               path: ['newRiderPhone'],
            });
         }
      }

      // Validate locations
      if (!data.pickupLocation) {
         ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Pickup location is required',
            path: ['pickupLocation'],
         });
      }

      if (!data.destinationLocation) {
         ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Destination location is required',
            path: ['destinationLocation'],
         });
      }

      // Validate pickup time for "later" option
      if (data.pickupType === 'later') {
         if (!data.pickupDate) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Pickup date is required for scheduled rides',
               path: ['pickupDate'],
            });
         }
         if (!data.pickupTime || data.pickupTime.trim() === '') {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Pickup time is required for scheduled rides',
               path: ['pickupTime'],
            });
         }

         // Validate that the selected datetime is at least 15 minutes in the future
         if (data.pickupDate && data.pickupTime) {
            const [hours, minutes] = data.pickupTime.split(':').map(Number);
            const selectedDateTime = set(data.pickupDate, { hours, minutes, seconds: 0 });
            const now = new Date();
            const minimumDateTime = addMinutes(now, 15);

            if (!isAfter(selectedDateTime, minimumDateTime)) {
               ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: 'Pickup time must be at least 15 minutes in the future',
                  path: ['pickupTime'],
               });
            }
         }
      }
   });

type BookRideFormData = z.infer<typeof bookRideSchema>;

export function BookRidePage() {
   const { withPermission } = useRoleBasedAccess();
   const router = useRouter();
   const riderTriggerRef = useRef<HTMLButtonElement>(null);

   // Local UI state (not in form)
   const [riderSearchQuery, setRiderSearchQuery] = useState('');
   const [riderComboboxOpen, setRiderComboboxOpen] = useState(false);
   const [pickupInput, setPickupInput] = useState('');
   const [destinationInput, setDestinationInput] = useState('');
   const [products, setProducts] = useState<RideSearchResult[]>([]);
   const [bookingSuccess, setBookingSuccess] = useState(false);
   const [createdRideId, setCreatedRideId] = useState<string | null>(null);

   // React Hook Form
   const {
      control,
      handleSubmit,
      watch,
      setValue,
      reset,
      formState: { errors, isValid },
   } = useForm<BookRideFormData>({
      resolver: zodResolver(bookRideSchema),
      mode: 'onChange',
      defaultValues: {
         riderType: 'existing',
         selectedRider: null,
         newRiderName: '',
         newRiderPhone: '',
         newRiderEmail: '',
         pickupLocation: null,
         destinationLocation: null,
         stops: [],
         selectedProductId: '',
         pickupType: 'now',
         pickupDate: undefined,
         pickupTime: '',
      },
   });

   // Watch form values
   const riderType = watch('riderType');
   const pickupLocation = watch('pickupLocation');
   const destinationLocation = watch('destinationLocation');
   const stops = watch('stops');
   const pickupType = watch('pickupType');
   const pickupDate = watch('pickupDate');

   // API hooks
   const searchMutation = useSearchRides();
   const createRideMutation = useCreateRide();
   const ridersQuery = useListRiders({
      page: 1,
      limit: 50,
      search: riderSearchQuery,
   });

   // Auto-trigger search when locations are selected
   useEffect(() => {
      if (pickupLocation && destinationLocation) {
         const stopsLocations = stops
            .filter(stop => stop.location !== null)
            .map(stop => stop.location!);

         searchMutation.mutate(
            {
               pickup: pickupLocation,
               destination: destinationLocation,
               stops: stopsLocations.length > 0 ? stopsLocations : undefined,
            },
            {
               onSuccess: response => {
                  setProducts(response.data);
                  if (response.data.length === 0 || !response.data[0]?.cityProduct?.id) {
                     toast.error('No available products found for this route');
                     setValue('selectedProductId', '');
                  } else {
                     // Auto-select first product - use cityProduct.id
                     setValue('selectedProductId', response.data[0].cityProduct.id);
                  }
               },
               onError: (error: any) => {
                  console.error('Search error:', error);
                  toast.error(error?.response?.data?.message || 'Failed to search for rides');
                  setProducts([]);
                  setValue('selectedProductId', '');
               },
            }
         );
      } else {
         // Clear products if either location is cleared
         setProducts([]);
         setValue('selectedProductId', '');
      }

      // eslint-disable-next-line react-hooks/exhaustive-deps
   }, [pickupLocation, destinationLocation, stops, searchMutation.mutate]);

   const handlePickupTypeChange = (value: PickupType) => {
      setValue('pickupType', value);

      // Auto-set date and time when "later" is selected
      if (value === 'later') {
         // Set default date to today
         if (!pickupDate) {
            setValue('pickupDate', new Date());
         }

         // Set default time to 20 minutes from now (buffer to avoid validation errors)
         if (!watch('pickupTime')) {
            const timeIn20Mins = addMinutes(new Date(), 20);
            const hours = timeIn20Mins.getHours().toString().padStart(2, '0');
            const minutes = timeIn20Mins.getMinutes().toString().padStart(2, '0');
            setValue('pickupTime', `${hours}:${minutes}`);
         }
      }
   };

   const handlePickupSelect = (location: Location) => {
      setValue('pickupLocation', location, { shouldValidate: true });
   };

   const handleDestinationSelect = (location: Location) => {
      setValue('destinationLocation', location, { shouldValidate: true });
   };

   const handleStopSelect = (index: number, location: Location) => {
      const newStops = [...stops];
      newStops[index].location = location;
      setValue('stops', newStops, { shouldValidate: true });
   };

   const handleStopInputChange = (index: number, value: string) => {
      const newStops = [...stops];
      newStops[index].input = value;
      setValue('stops', newStops);
   };

   const addStop = () => {
      if (stops.length < 5) {
         setValue('stops', [...stops, { location: null, input: '' }]);
      }
   };

   const removeStop = (index: number) => {
      setValue(
         'stops',
         stops.filter((_, i) => i !== index)
      );
   };

   const handleRiderTypeChange = (value: string) => {
      setValue('riderType', value as RiderType, { shouldValidate: true });
      // Clear rider data when switching types
      setValue('selectedRider', null);
      setValue('newRiderName', '');
      setValue('newRiderPhone', '');
      setValue('newRiderEmail', '');
      setRiderSearchQuery('');
      setRiderComboboxOpen(false);
   };

   const onSubmit = (data: BookRideFormData) => {
      withPermission(RBAC_PERMISSIONS.BOOK_RIDE.CREATE, () => {
         const stopsLocations = data.stops
            .filter(stop => stop.location !== null)
            .map(stop => stop.location!);

         // Convert pickup date/time to UTC ISO string if pickupType is 'later'
         let pickupTimeUTC: string | undefined = undefined;
         if (data.pickupType === 'later' && data.pickupDate && data.pickupTime) {
            const [hours, minutes] = data.pickupTime.split(':').map(Number);
            const localDateTime = set(data.pickupDate, { hours, minutes, seconds: 0 });
            pickupTimeUTC = formatISO(localDateTime);
         }

         createRideMutation.mutate(
            {
               pickup: data.pickupLocation!,
               destination: data.destinationLocation!,
               stops: stopsLocations.length > 0 ? stopsLocations : undefined,
               cityProductId: data.selectedProductId,
               riderId:
                  data.riderType === 'existing' && data.selectedRider
                     ? data.selectedRider.id
                     : undefined,
               riderMeta: {
                  name:
                     data.riderType === 'existing' && data.selectedRider
                        ? `${data.selectedRider.firstName} ${data.selectedRider.lastName}`
                        : data.newRiderName || '',
                  phoneNumber:
                     data.riderType === 'existing' && data.selectedRider
                        ? data.selectedRider.user.phoneNumber
                        : data.newRiderPhone || '',
                  email:
                     data.riderType === 'existing' && data.selectedRider
                        ? data.selectedRider.user.email || undefined
                        : data.newRiderEmail || undefined,
               },
               pickupType: data.pickupType,
               pickupTime: pickupTimeUTC,
            },
            {
               onSuccess: response => {
                  toast.success('Ride booked successfully!');
                  setBookingSuccess(true);
                  setCreatedRideId(response.data.id);
                  // Don't clear form yet - show success card first
               },
               onError: (error: any) => {
                  console.error('Booking error:', error);
                  toast.error(error?.response?.data?.message || 'Failed to book ride');
               },
            }
         );
      });
   };

   const handleViewRideDetails = () => {
      if (createdRideId) {
         router.push(`/dashboard/rides?rideId=${createdRideId}`);
      }
   };

   const handleBookAnother = () => {
      // Clear all form data
      setBookingSuccess(false);
      setCreatedRideId(null);
      setRiderSearchQuery('');
      setRiderComboboxOpen(false);
      setPickupInput('');
      setDestinationInput('');
      setProducts([]);
      reset();
   };

   const isSearching = searchMutation.isPending;
   const isBooking = createRideMutation.isPending;
   const productsAvailable = products.length > 0;

   const canBook = isValid && !isBooking;

   // Show success card if booking was successful
   if (bookingSuccess && createdRideId) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex justify-between items-center'>
               <h2 className='text-2xl font-semibold text-gray-900'>Book a Ride</h2>
            </div>

            <Card className='p-6 max-w-2xl'>
               <div className='flex flex-col items-center gap-4 py-8'>
                  <div className='rounded-full bg-green-100 p-4'>
                     <svg
                        className='h-8 w-8 text-green-600'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                     >
                        <path
                           strokeLinecap='round'
                           strokeLinejoin='round'
                           strokeWidth={2}
                           d='M5 13l4 4L19 7'
                        />
                     </svg>
                  </div>
                  <h3 className='text-xl font-semibold text-gray-900'>
                     Ride Created Successfully!
                  </h3>
                  <p className='text-sm text-gray-600 text-center'>
                     The ride has been booked. You can view the ride details or book another ride.
                  </p>
                  <div className='flex gap-3 mt-4'>
                     <Button onClick={handleViewRideDetails} size='lg'>
                        View Ride Details
                     </Button>
                     <Button onClick={handleBookAnother} variant='outline' size='lg'>
                        Book Another Ride
                     </Button>
                  </div>
               </div>
            </Card>
         </div>
      );
   }

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Book a Ride</h2>
         </div>

         <Card className='p-6 max-w-2xl'>
            <div className='space-y-6'>
               {/* Rider Selection */}
               <div className='flex flex-col gap-4'>
                  <Label>Select Rider Type *</Label>
                  <Controller
                     control={control}
                     name='riderType'
                     render={({ field }) => (
                        <RadioGroup
                           value={field.value}
                           onValueChange={value => {
                              handleRiderTypeChange(value);
                           }}
                           className='flex flex-row gap-6'
                        >
                           <div className='flex items-center space-x-2'>
                              <RadioGroupItem value='existing' id='existing' />
                              <Label htmlFor='existing' className='font-normal cursor-pointer'>
                                 Existing Rider
                              </Label>
                           </div>
                           <div className='flex items-center space-x-2'>
                              <RadioGroupItem value='new' id='new' />
                              <Label htmlFor='new' className='font-normal cursor-pointer'>
                                 New Rider
                              </Label>
                           </div>
                        </RadioGroup>
                     )}
                  />

                  {/* Existing Rider Selection */}
                  {riderType === 'existing' && (
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='rider-select'>Search and Select Rider *</Label>
                        <Controller
                           control={control}
                           name='selectedRider'
                           render={({ field }) => (
                              <Popover open={riderComboboxOpen} onOpenChange={setRiderComboboxOpen}>
                                 <PopoverTrigger asChild>
                                    <Button
                                       ref={riderTriggerRef}
                                       variant='outline'
                                       role='combobox'
                                       aria-expanded={riderComboboxOpen}
                                       className='w-full justify-between'
                                    >
                                       {field.value ? (
                                          <div className='flex flex-col items-start'>
                                             <span>{`${field.value.firstName} ${field.value.lastName}`}</span>
                                             <span className='text-xs text-gray-500'>
                                                {field.value.user.phoneNumber}
                                             </span>
                                          </div>
                                       ) : (
                                          <span className='text-muted-foreground'>
                                             Search riders...
                                          </span>
                                       )}
                                       <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                                    </Button>
                                 </PopoverTrigger>
                                 <PopoverContent
                                    className='p-0'
                                    align='start'
                                    style={{ width: riderTriggerRef.current?.offsetWidth }}
                                 >
                                    <Command shouldFilter={false}>
                                       <CommandInput
                                          placeholder='Search by name, email, or phone...'
                                          value={riderSearchQuery}
                                          onValueChange={setRiderSearchQuery}
                                       />
                                       <CommandList>
                                          <CommandEmpty>
                                             {ridersQuery.isLoading
                                                ? 'Loading...'
                                                : 'No riders found.'}
                                          </CommandEmpty>
                                          <CommandGroup>
                                             {ridersQuery.data?.data &&
                                                ridersQuery.data.data.slice(0, 10).map(rider => (
                                                   <CommandItem
                                                      className='w-full'
                                                      key={rider.id}
                                                      value={rider.id}
                                                      onSelect={() => {
                                                         field.onChange(rider);
                                                         setRiderComboboxOpen(false);
                                                      }}
                                                   >
                                                      <Check
                                                         className={cn(
                                                            'mr-2 h-4 w-4',
                                                            field.value?.id === rider.id
                                                               ? 'opacity-100'
                                                               : 'opacity-0'
                                                         )}
                                                      />
                                                      <div className='flex flex-col'>
                                                         <span>{`${rider.firstName} ${rider.lastName}`}</span>
                                                         <span className='text-xs text-gray-500'>
                                                            {rider.user.phoneNumber}
                                                         </span>
                                                      </div>
                                                   </CommandItem>
                                                ))}
                                          </CommandGroup>
                                       </CommandList>
                                    </Command>
                                 </PopoverContent>
                              </Popover>
                           )}
                        />
                        {errors.selectedRider && (
                           <p className='text-sm text-red-500'>{errors.selectedRider.message}</p>
                        )}
                     </div>
                  )}

                  {/* New Rider Form */}
                  {riderType === 'new' && (
                     <div className='flex flex-col gap-4'>
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='new-rider-name'>Rider Name *</Label>
                           <Controller
                              control={control}
                              name='newRiderName'
                              render={({ field }) => (
                                 <Input
                                    id='new-rider-name'
                                    placeholder='Enter rider name'
                                    {...field}
                                 />
                              )}
                           />
                           {errors.newRiderName && (
                              <p className='text-sm text-red-500'>{errors.newRiderName.message}</p>
                           )}
                        </div>
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='new-rider-phone'>Phone Number *</Label>
                           <Controller
                              control={control}
                              name='newRiderPhone'
                              render={({ field }) => (
                                 <PhoneInput
                                    id='new-rider-phone'
                                    international
                                    countryCallingCodeEditable={false}
                                    defaultCountry='IN'
                                    value={field.value}
                                    limitMaxLength={true}
                                    onChange={value => field.onChange(value || '')}
                                    className='flex h-10 outline-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0 w-full rounded-md border border-input bg-background px-3 py-0 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
                                 />
                              )}
                           />
                           {errors.newRiderPhone && (
                              <p className='text-sm text-red-500'>{errors.newRiderPhone.message}</p>
                           )}
                        </div>
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='new-rider-email'>Email (Optional)</Label>
                           <Controller
                              control={control}
                              name='newRiderEmail'
                              render={({ field }) => (
                                 <Input
                                    id='new-rider-email'
                                    type='email'
                                    placeholder='<EMAIL>'
                                    {...field}
                                 />
                              )}
                           />
                           {errors.newRiderEmail && (
                              <p className='text-sm text-red-500'>{errors.newRiderEmail.message}</p>
                           )}
                        </div>
                     </div>
                  )}
               </div>
               {/* Pickup Location */}
               <LocationSearch
                  label='Pickup Location *'
                  placeholder='Search for pickup location...'
                  value={pickupInput}
                  onLocationSelect={handlePickupSelect}
                  onInputChange={setPickupInput}
                  currentLocation={pickupLocation}
               />

               {/* Stops Section */}
               <div className='flex flex-col gap-3'>
                  <div className='flex items-center justify-between'>
                     <Label>Stops (Optional)</Label>
                     <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={addStop}
                        disabled={stops.length >= 5}
                        className='flex items-center gap-1'
                     >
                        <Plus className='h-4 w-4' />
                        Add Stop {stops.length >= 5 && '(Max 5)'}
                     </Button>
                  </div>

                  {stops.map((stop, index) => (
                     <div key={index} className='flex gap-2 items-end'>
                        <div className='flex-1'>
                           <LocationSearch
                              label={`Stop ${index + 1}`}
                              placeholder='Search for stop location...'
                              value={stop.input}
                              onLocationSelect={location => handleStopSelect(index, location)}
                              onInputChange={value => handleStopInputChange(index, value)}
                              biasLocation={pickupLocation}
                              currentLocation={stop.location}
                           />
                        </div>
                        <Button
                           type='button'
                           variant='ghost'
                           size='icon'
                           onClick={() => removeStop(index)}
                           className='mb-0.5'
                        >
                           <X className='h-4 w-4' />
                        </Button>
                     </div>
                  ))}
               </div>

               {/* Destination Location */}
               <LocationSearch
                  label='Destination Location *'
                  placeholder='Search for destination...'
                  value={destinationInput}
                  onLocationSelect={handleDestinationSelect}
                  onInputChange={setDestinationInput}
                  biasLocation={pickupLocation}
                  currentLocation={destinationLocation}
               />

               {/* Product Selection */}
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='product'>Select Product *</Label>
                  <Controller
                     control={control}
                     name='selectedProductId'
                     render={({ field }) => (
                        <Select
                           value={field.value}
                           onValueChange={field.onChange}
                           disabled={!productsAvailable || isSearching}
                        >
                           <SelectTrigger id='product' className='w-full'>
                              <SelectValue
                                 placeholder={
                                    isSearching
                                       ? 'Searching for available products...'
                                       : !pickupLocation || !destinationLocation
                                       ? 'Select pickup and destination first'
                                       : productsAvailable
                                       ? 'Select a product'
                                       : 'No products available for this route'
                                 }
                              />
                           </SelectTrigger>
                           <SelectContent>
                              {products
                                 .filter(product => product?.cityProduct?.id)
                                 .map(product => (
                                    <SelectItem
                                       key={product.cityProduct.id}
                                       value={product.cityProduct.id}
                                    >
                                       <div className='flex items-center justify-between gap-4 w-full'>
                                          <span>{product.name}</span>
                                          <span className='text-sm text-gray-500'>
                                             ₹{product.price?.toFixed(2) ?? '0.00'}
                                          </span>
                                       </div>
                                    </SelectItem>
                                 ))}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  {isSearching && (
                     <div className='flex items-center gap-2 text-sm text-gray-500'>
                        <Spinner className='h-4 w-4' />
                        <span>Searching for available products...</span>
                     </div>
                  )}
                  {errors.selectedProductId && (
                     <p className='text-sm text-red-500'>{errors.selectedProductId.message}</p>
                  )}
               </div>

               {/* Pickup Type Selection */}
               <div className='flex flex-col gap-4'>
                  <Label>Pickup Type *</Label>
                  <Controller
                     control={control}
                     name='pickupType'
                     render={({ field }) => (
                        <RadioGroup
                           value={field.value}
                           onValueChange={value => handlePickupTypeChange(value as PickupType)}
                           className='flex flex-row gap-6'
                        >
                           <div className='flex items-center space-x-2'>
                              <RadioGroupItem value='now' id='pickup-now' />
                              <Label htmlFor='pickup-now' className='font-normal cursor-pointer'>
                                 Book Now
                              </Label>
                           </div>
                           <div className='flex items-center space-x-2'>
                              <RadioGroupItem value='later' id='pickup-later' />
                              <Label htmlFor='pickup-later' className='font-normal cursor-pointer'>
                                 Schedule for Later
                              </Label>
                           </div>
                        </RadioGroup>
                     )}
                  />
               </div>

               {/* Pickup Date and Time (shown only when pickupType is 'later') */}
               {pickupType === 'later' && (
                  <div className='border rounded-md p-4 space-y-4 bg-background'>
                     <Label className='text-sm font-semibold'>Schedule Pickup Time *</Label>
                     <p className='text-xs text-muted-foreground'>
                        Select the date and time for the scheduled pickup.
                     </p>

                     <div className='grid grid-cols-2 gap-4'>
                        <div className='flex flex-col gap-2'>
                           <Label className='text-xs'>Pickup Date *</Label>
                           <Controller
                              control={control}
                              name='pickupDate'
                              render={({ field }) => (
                                 <DatePicker
                                    value={field.value}
                                    onChange={field.onChange}
                                    placeholder='Select date'
                                    disablePastDates={true}
                                 />
                              )}
                           />
                           {errors.pickupDate && (
                              <p className='text-xs text-red-500'>{errors.pickupDate.message}</p>
                           )}
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label className='text-xs'>Pickup Time *</Label>
                           <Controller
                              control={control}
                              name='pickupTime'
                              render={({ field }) => {
                                 // Calculate minimum time if today is selected (15 minutes from now)
                                 let minTime: string | undefined = undefined;
                                 if (pickupDate) {
                                    const today = new Date();
                                    const selectedDate = startOfDay(pickupDate);
                                    const todayDate = startOfDay(today);

                                    // If selected date is today, set min time to 15 minutes from now
                                    if (selectedDate.getTime() === todayDate.getTime()) {
                                       const timeIn15Mins = addMinutes(today, 15);
                                       const hours = timeIn15Mins
                                          .getHours()
                                          .toString()
                                          .padStart(2, '0');
                                       const minutes = timeIn15Mins
                                          .getMinutes()
                                          .toString()
                                          .padStart(2, '0');
                                       minTime = `${hours}:${minutes}`;
                                    }
                                 }

                                 return (
                                    <Input
                                       type='time'
                                       {...field}
                                       min={minTime}
                                       className='bg-background'
                                    />
                                 );
                              }}
                           />
                           {errors.pickupTime && (
                              <p className='text-xs text-red-500'>{errors.pickupTime.message}</p>
                           )}
                        </div>
                     </div>
                  </div>
               )}

               {/* Book Ride Button */}
               <Button
                  onClick={handleSubmit(onSubmit)}
                  disabled={!canBook}
                  className='w-full'
                  size='lg'
               >
                  {isBooking ? (
                     <>
                        <Spinner className='mr-2 h-4 w-4' />
                        Booking Ride...
                     </>
                  ) : (
                     'Book Ride'
                  )}
               </Button>
            </div>
         </Card>
      </div>
   );
}
