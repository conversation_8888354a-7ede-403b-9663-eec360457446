'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useListRides, useGetRideDetails } from '../api/queries';
import { useListProduct } from '@/modules/product/api/queries';
import { RideTable } from '../components/ride-table';
import { RideFilters } from '../components/ride-filters';
import { RideDetailsView } from '../components/ride-details-view';
import { RideExportModal } from '../components/ride-export-modal';
import { CancelRideModal } from '../components/cancel-ride-modal';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ArrowLeft, Download, Ban } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';

export function RidePage() {
   const router = useRouter();
   const searchParams = useSearchParams();
   const queryClient = useQueryClient();
   const rideId = searchParams.get('rideId');
   const [page, setPage] = useState(1);
   const [limit] = useState(9);
   const [status, setStatus] = useState<string>('');
   const [fromDate, setFromDate] = useState<string>('');
   const [toDate, setToDate] = useState<string>('');
   const [searchQuery, setSearchQuery] = useState<string>('');
   const [searchType, setSearchType] = useState<string>('riderPhoneNumber');
   const [productId, setProductId] = useState<string>('');
   const [driverId, setDriverId] = useState<string>('');
   const [isExportModalOpen, setIsExportModalOpen] = useState(false);
   const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

   // Build search params based on search type
   const searchParamsObj: {
      riderName?: string;
      driverName?: string;
      vehicleNumber?: string;
      riderPhoneNumber?: string;
      driverPhoneNumber?: string;
   } = {};

   if (searchQuery && searchType) {
      if (searchType === 'riderName') {
         searchParamsObj.riderName = searchQuery;
      } else if (searchType === 'driverName') {
         searchParamsObj.driverName = searchQuery;
      } else if (searchType === 'vehicleNumber') {
         searchParamsObj.vehicleNumber = searchQuery;
      } else if (searchType === 'riderPhoneNumber') {
         searchParamsObj.riderPhoneNumber = searchQuery;
      } else if (searchType === 'driverPhoneNumber') {
         searchParamsObj.driverPhoneNumber = searchQuery;
      }
   }

   // Fetch products for filter
   const listProducts = useListProduct({
      page: 1,
      limit: 100,
      isEnabled: 'true',
   });

   const listRides = useListRides({
      page,
      limit,
      status: status && status !== 'all' ? status : undefined,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
      productId: productId && productId !== 'all' ? productId : undefined,
      driverId: driverId || undefined,
      ...searchParamsObj,
   });

   // Fetch ride details if rideId is in URL
   const getRideDetailsQuery = useGetRideDetails(rideId);
   const rideDetails = getRideDetailsQuery.data?.data;

   const handleClearFilters = () => {
      setStatus('');
      setFromDate('');
      setToDate('');
      setSearchQuery('');
      setSearchType('riderPhoneNumber');
      setProductId('');
      setDriverId('');
      setPage(1);
   };

   const handleBackToList = () => {
      router.back();
   };

   // Check if ride can be cancelled
   const canCancelRide = (status: string) => {
      if (!status) return false;
      const normalizedStatus = status.toLowerCase().trim();
      const nonCancellableStatuses = ['trip_completed', 'cancelled', 'trip_started'];
      return !nonCancellableStatuses.includes(normalizedStatus);
   };

   const handleCancelSuccess = () => {
      queryClient.invalidateQueries({ queryKey: ['ride-details', rideId] });
      queryClient.invalidateQueries({ queryKey: ['rides'] });
      getRideDetailsQuery.refetch();
   };

   // Show loading state while fetching ride details
   if (rideId && getRideDetailsQuery.isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex items-center gap-3'>
               <Button variant='ghost' size='sm' disabled>
                  <ArrowLeft className='h-4 w-4' />
               </Button>
               <h2 className='text-2xl font-semibold text-gray-900'>Loading...</h2>
            </div>
            <Card className='overflow-hidden py-6 px-6 rounded-sm'>
               <div className='space-y-4'>
                  <div className='h-6 bg-gray-200 rounded animate-pulse w-32' />
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-48' />
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
               </div>
            </Card>
         </div>
      );
   }

   // Render details view when rideId is present in URL
   if (rideId && rideDetails) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex items-center justify-between'>
               <div className='flex items-center gap-3'>
                  <Button
                     variant='ghost'
                     size='sm'
                     onClick={handleBackToList}
                     className='cursor-pointer'
                  >
                     <ArrowLeft className='h-4 w-4' />
                  </Button>
                  <h2 className='text-2xl font-semibold text-gray-900'>Ride Details</h2>
               </div>
               {canCancelRide(rideDetails.status) && (
                  <Button
                     variant='outline'
                     onClick={() => setIsCancelModalOpen(true)}
                     className='gap-2 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700'
                  >
                     <Ban className='h-4 w-4' />
                     Cancel Ride
                  </Button>
               )}
            </div>

            <RideDetailsView rideDetails={rideDetails} />

            {/* Cancel Ride Modal */}
            <CancelRideModal
               isOpen={isCancelModalOpen}
               onClose={() => setIsCancelModalOpen(false)}
               rideId={rideId}
               onCancelSuccess={handleCancelSuccess}
            />
         </div>
      );
   }

   // Render list view
   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Rides</h2>
            <Button variant='outline' onClick={() => setIsExportModalOpen(true)} className='gap-2'>
               <Download className='h-4 w-4' />
               Export CSV
            </Button>
         </div>

         <RideFilters
            status={status}
            fromDate={fromDate}
            toDate={toDate}
            searchQuery={searchQuery}
            searchType={searchType}
            productId={productId}
            driverId={driverId}
            products={listProducts.data?.data || []}
            onStatusChange={setStatus}
            onFromDateChange={setFromDate}
            onToDateChange={setToDate}
            onSearchQueryChange={setSearchQuery}
            onSearchTypeChange={setSearchType}
            onProductIdChange={setProductId}
            onDriverIdChange={setDriverId}
            onClearFilters={handleClearFilters}
         />

         <RideTable
            data={listRides.data}
            isLoading={listRides.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
         />

         <RideExportModal isOpen={isExportModalOpen} onClose={() => setIsExportModalOpen(false)} />
      </div>
   );
}
