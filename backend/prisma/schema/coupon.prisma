enum DiscountType {
  FLAT
  PERCENTAGE
}

enum ApplicabilityType {
  CITY
  PRODUCT
  CITY_PRODUCT
  USER
}

model Coupon {
  id                  String            @id @default(uuid()) @db.Uuid
  name                String            @map("name")
  code                String            @unique @map("code")
  thumbnail           String?           @map("thumbnail")
  description         String?           @map("description") @db.Text
  startDate           DateTime          @map("start_date") @db.Timestamptz
  endDate             DateTime          @map("end_date") @db.Timestamptz
  discountType        DiscountType      @map("discount_type")
  discountValue       Decimal           @map("discount_value") @db.Decimal(10, 2)
  maxDiscountLimit    Decimal?          @map("max_discount_limit") @db.Decimal(10, 2)
  minFareCondition    Decimal?          @map("min_fare_condition") @db.Decimal(10, 2)
  usageLimit          Int               @map("usage_limit")
  usageCount          Int               @default(0) @map("usage_count")
  applicabilityType   ApplicabilityType @map("applicability_type")
  applyConditionLogic Json?             @map("apply_condition_logic") @db.JsonB
  metadata            Json?             @map("metadata") @db.JsonB
  isActive            Boolean           @default(true) @map("is_active")
  createdAt           DateTime          @default(now()) @map("created_at")
  updatedAt           DateTime          @updatedAt @map("updated_at")
  deletedAt           DateTime?         @map("deleted_at") @db.Timestamptz

  // Relations
  couponZones          CouponZone[]
  couponProducts       CouponProduct[]
  couponCityProducts   CouponCityProduct[]
  couponUsers          CouponUser[]
  CouponUsage          CouponUsage[]
  UserCouponUsageLimit UserCouponUsageLimit[]

  @@index([code], name: "idx_coupon_code")
  @@index([applicabilityType], name: "idx_coupon_applicability_type")
  @@index([isActive], name: "idx_coupon_is_active")
  @@index([startDate, endDate], name: "idx_coupon_date_range")
  @@index([usageCount, usageLimit], name: "idx_coupon_usage")
  @@map("coupons")
}

model CouponZone {
  id       String @id @default(uuid()) @db.Uuid
  couponId String @map("coupon_id") @db.Uuid
  zoneId   String @map("zone_id") @db.Uuid

  // Relations
  coupon Coupon @relation(fields: [couponId], references: [id], onDelete: Cascade)
  zone   Zone   @relation(fields: [zoneId], references: [id], onDelete: Cascade)

  @@unique([couponId, zoneId], name: "unique_coupon_zone")
  @@index([couponId], name: "idx_coupon_zone_coupon_id")
  @@index([zoneId], name: "idx_coupon_zone_zone_id")
  @@map("coupon_zones")
}

model CouponProduct {
  id        String @id @default(uuid()) @db.Uuid
  couponId  String @map("coupon_id") @db.Uuid
  productId String @map("product_id") @db.Uuid

  // Relations
  coupon  Coupon  @relation(fields: [couponId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([couponId, productId], name: "unique_coupon_product")
  @@index([couponId], name: "idx_coupon_product_coupon_id")
  @@index([productId], name: "idx_coupon_product_product_id")
  @@map("coupon_products")
}

model CouponCityProduct {
  id            String @id @default(uuid()) @db.Uuid
  couponId      String @map("coupon_id") @db.Uuid
  cityProductId String @map("city_product_id") @db.Uuid

  // Relations
  coupon      Coupon      @relation(fields: [couponId], references: [id], onDelete: Cascade)
  cityProduct CityProduct @relation(fields: [cityProductId], references: [id], onDelete: Cascade)

  @@unique([couponId, cityProductId], name: "unique_coupon_city_product")
  @@index([couponId], name: "idx_coupon_city_product_coupon_id")
  @@index([cityProductId], name: "idx_coupon_city_product_city_product_id")
  @@map("coupon_city_products")
}

model CouponUser {
  id       String @id @default(uuid()) @db.Uuid
  couponId String @map("coupon_id") @db.Uuid
  userId   String @map("user_id") @db.Uuid

  // Relations
  coupon      Coupon      @relation(fields: [couponId], references: [id], onDelete: Cascade)
  userProfile UserProfile @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([couponId, userId], name: "unique_coupon_user")
  @@index([couponId], name: "idx_coupon_user_coupon_id")
  @@index([userId], name: "idx_coupon_user_user_id")
  @@map("coupon_users")
}
