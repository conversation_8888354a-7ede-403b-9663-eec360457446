import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Patch,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { CouponService } from '@shared/shared/modules/coupon/coupon.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../../docs/swagger/common-responses.dto';
import {
  CreateCouponWithApplicabilityDto,
  UpdateCouponWithApplicabilityDto,
  CouponFilterDto,
  CouponResponseDto,
  CouponListResponseDto,
} from './dto';
import { ApplicabilityType } from '@shared/shared/repositories/models/coupon.model';

@ApiTags('Admin - Coupons')
@Controller('admin/coupons')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AdminCouponController {
  constructor(private readonly couponService: CouponService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new coupon',
    description: 'Create a new coupon with applicability rules',
  })
  @ApiResponse({
    status: 201,
    type: ApiResponseDto<CouponResponseDto>,
    description: 'Coupon created successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  async createCoupon(
    @Body() createCouponDto: CreateCouponWithApplicabilityDto,
  ) {
    const { coupon, applicability } = createCouponDto;

    // Map applicability data
    const applicabilityData = {
      type: coupon.applicabilityType,
      zoneIds: applicability.zoneIds || undefined,
      productIds: applicability.productIds || undefined,
      cityProductIds: applicability.cityProductIds || undefined,
      userIds: applicability.userIds || undefined,
    };

    const data = await this.couponService.createCoupon(
      coupon,
      applicabilityData,
    );

    return {
      success: true,
      message: 'Coupon created successfully',
      data: this.mapToResponseDto(data),
      timestamp: Date.now(),
    };
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'List all coupons',
    description: 'Retrieve coupons with pagination, filters, and search',
  })
  @ApiResponse({
    status: 200,
    type: CouponListResponseDto,
    description: 'Coupons retrieved successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  async listCoupons(
    @Query() filters: CouponFilterDto,
  ): Promise<CouponListResponseDto> {
    const result = await this.couponService.listCoupons({
      search: filters.search || undefined,
      applicabilityType: filters.applicabilityType || undefined,
      discountType: filters.discountType || undefined,
      isActive: filters.isActive !== undefined ? filters.isActive : undefined,
      cityId: filters.cityId || undefined,
      productId: filters.productId || undefined,
      isExpired:
        filters.isExpired !== undefined ? filters.isExpired : undefined,
      page: filters.page || undefined,
      limit: filters.limit || undefined,
      sortBy: filters.sortBy || undefined,
      sortOrder: filters.sortOrder || undefined,
    });

    return {
      success: true,
      message: 'Coupons retrieved successfully',
      data: result.data.map((coupon) => this.mapToResponseDto(coupon)),
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get coupon by ID',
    description:
      'Retrieve detailed coupon information including all applicability assignments',
  })
  @ApiParam({
    name: 'id',
    description: 'Coupon ID',
    example: 'coupon-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<CouponResponseDto>,
    description: 'Coupon retrieved successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async getCouponById(@Param('id', ParseUUIDPipe) id: string) {
    const data = await this.couponService.getCouponById(id);

    return {
      success: true,
      message: 'Coupon retrieved successfully',
      data: this.mapToResponseDto(data),
      timestamp: Date.now(),
    };
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update coupon',
    description: 'Update coupon details and applicability rules',
  })
  @ApiParam({
    name: 'id',
    description: 'Coupon ID',
    example: 'coupon-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<CouponResponseDto>,
    description: 'Coupon updated successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  async updateCoupon(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCouponDto: UpdateCouponWithApplicabilityDto,
  ) {
    const { coupon, applicability } = updateCouponDto;

    // Map applicability data if provided
    let applicabilityData;
    if (applicability) {
      applicabilityData = {
        type: coupon?.applicabilityType || ApplicabilityType.CITY, // Default or from coupon data
        zoneIds: applicability.zoneIds || undefined,
        productIds: applicability.productIds || undefined,
        cityProductIds: applicability.cityProductIds || undefined,
        userIds: applicability.userIds || undefined,
      };
    }

    const data = await this.couponService.updateCoupon(
      id,
      coupon || {},
      applicabilityData,
    );

    return {
      success: true,
      message: 'Coupon updated successfully',
      data: this.mapToResponseDto(data),
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete coupon',
    description: 'Soft delete a coupon (sets deletedAt timestamp)',
  })
  @ApiParam({
    name: 'id',
    description: 'Coupon ID',
    example: 'coupon-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<CouponResponseDto>,
    description: 'Coupon deleted successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async deleteCoupon(@Param('id', ParseUUIDPipe) id: string) {
    const data = await this.couponService.deleteCoupon(id);

    return {
      success: true,
      message: 'Coupon deleted successfully',
      data: this.mapToResponseDto(data),
      timestamp: Date.now(),
    };
  }

  @Patch(':id/toggle-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Toggle coupon status',
    description: 'Activate or deactivate a coupon',
  })
  @ApiParam({
    name: 'id',
    description: 'Coupon ID',
    example: 'coupon-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: ApiResponseDto<CouponResponseDto>,
    description: 'Coupon status toggled successfully',
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async toggleCouponStatus(@Param('id', ParseUUIDPipe) id: string) {
    const data = await this.couponService.toggleCouponStatus(id);

    return {
      success: true,
      message: `Coupon ${data.isActive ? 'activated' : 'deactivated'} successfully`,
      data: this.mapToResponseDto(data),
      timestamp: Date.now(),
    };
  }

  /**
   * Map coupon entity to response DTO
   */
  private mapToResponseDto(coupon: any): CouponResponseDto {
    return {
      id: coupon.id,
      name: coupon.name,
      code: coupon.code,
      thumbnail: coupon.thumbnail,
      description: coupon.description,
      startDate: coupon.startDate,
      endDate: coupon.endDate,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
      maxDiscountLimit: coupon.maxDiscountLimit,
      minFareCondition: coupon.minFareCondition,
      usageLimit: coupon.usageLimit,
      usageCount: coupon.usageCount,
      applicabilityType: coupon.applicabilityType,
      applyConditionLogic: coupon.applyConditionLogic,
      metadata: coupon.metadata,
      isActive: coupon.isActive,
      createdAt: coupon.createdAt,
      updatedAt: coupon.updatedAt,
      couponZones: coupon.couponZones,
      couponProducts: coupon.couponProducts,
      couponCityProducts: coupon.couponCityProducts,
      couponUsers: coupon.couponUsers,
      applicabilitySummary: this.generateApplicabilitySummary(coupon),
    };
  }

  /**
   * Generate human-readable applicability summary
   */
  private generateApplicabilitySummary(coupon: any): string {
    switch (coupon.applicabilityType) {
      case ApplicabilityType.CITY:
        if (coupon.couponZones && coupon.couponZones.length > 0) {
          const cities = [
            ...new Set(
              coupon.couponZones
                .filter((cz: any) => cz.zone?.city?.name)
                .map((cz: any) => cz.zone.city.name),
            ),
          ];
          return `Applicable to: Zones in ${cities.join(', ')}`;
        }
        return 'Applicable to: Selected zones';

      case ApplicabilityType.PRODUCT:
        if (coupon.couponProducts && coupon.couponProducts.length > 0) {
          const products = coupon.couponProducts
            .filter((cp: any) => cp.product?.name)
            .map((cp: any) => cp.product.name);
          return `Applicable to: ${products.join(', ')}`;
        }
        return 'Applicable to: Selected products';

      case ApplicabilityType.CITY_PRODUCT:
        if (coupon.couponCityProducts && coupon.couponCityProducts.length > 0) {
          const combinations = coupon.couponCityProducts
            .filter(
              (ccp: any) =>
                ccp.cityProduct?.city?.name && ccp.cityProduct?.product?.name,
            )
            .map(
              (ccp: any) =>
                `${ccp.cityProduct.product.name} in ${ccp.cityProduct.city.name}`,
            );
          return `Applicable to: ${combinations.join(', ')}`;
        }
        return 'Applicable to: Selected city-product combinations';

      case ApplicabilityType.USER:
        if (coupon.couponUsers && coupon.couponUsers.length > 0) {
          return `Applicable to: ${coupon.couponUsers.length} specific user(s)`;
        }
        return 'Applicable to: Selected users';

      default:
        return 'Applicability not defined';
    }
  }
}
