import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { AddProductsToCityDto } from './dto/add-products-to-city.dto';
import { RemoveProductsFromCityDto } from './dto/remove-products-from-city.dto';
import { ListCityProductsDto } from './dto/list-city-products.dto';
import {
  CityProductAddApiResponseDto,
  CityProductRemoveApiResponseDto,
  CityProductEnableApiResponseDto,
  CityProductDisableApiResponseDto,
  CityProductPaginatedApiResponseDto,
  CityProductListApiResponseDto,
} from './dto/city-product-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { CityProductService } from '@shared/shared/modules/city-product/city-product.service';

@ApiTags('City Products')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('city-products')
export class CityProductController {
  constructor(private readonly cityProductService: CityProductService) { }

  @Post('cities/:cityId/add-products')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Add multiple products to a city' })
  @ApiParam({ name: 'cityId', description: 'City ID', type: 'string' })
  @ApiResponse({ status: 201, type: CityProductAddApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async addProductsToCity(
    @Param('cityId') cityId: string,
    @Body() body: AddProductsToCityDto,
  ) {
    const data = await this.cityProductService.addProductsToCity(
      cityId,
      body.products,
    );
    return {
      success: true,
      message: 'Products added to city successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post('cities/:cityId/remove-products')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Remove multiple products from a city' })
  @ApiParam({ name: 'cityId', description: 'City ID', type: 'string' })
  @ApiResponse({ status: 200, type: CityProductRemoveApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async removeProductsFromCity(
    @Param('cityId') cityId: string,
    @Body() body: RemoveProductsFromCityDto,
  ) {
    await this.cityProductService.removeProductsFromCity(cityId, body.products);
    return {
      success: true,
      message: 'Products removed from city successfully',
      timestamp: Date.now(),
    };
  }

  @Patch(':id/enable')
  @ApiOperation({ summary: 'Enable a city product' })
  @ApiParam({ name: 'id', description: 'City Product ID', type: 'string' })
  @ApiResponse({ status: 200, type: CityProductEnableApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async enableCityProduct(@Param('id') id: string) {
    const data = await this.cityProductService.enableCityProduct(id);
    return {
      success: true,
      message: 'City product enabled successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id/disable')
  @ApiOperation({ summary: 'Disable a city product' })
  @ApiParam({ name: 'id', description: 'City Product ID', type: 'string' })
  @ApiResponse({ status: 200, type: CityProductDisableApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async disableCityProduct(@Param('id') id: string) {
    const data = await this.cityProductService.disableCityProduct(id);
    return {
      success: true,
      message: 'City product disabled successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('cities/:cityId/paginate')
  @ApiOperation({ summary: 'Get paginated city products with filters' })
  @ApiParam({ name: 'cityId', description: 'City ID', type: 'string' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by product name',
  })
  @ApiQuery({
    name: 'productName',
    required: false,
    type: String,
    description: 'Filter by product name',
  })
  @ApiQuery({
    name: 'vehicleTypeId',
    required: false,
    type: String,
    description: 'Filter by vehicle type ID',
  })
  @ApiResponse({ status: 200, type: CityProductPaginatedApiResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async paginateCityProducts(
    @Param('cityId') cityId: string,
    @Query()
    query: PaginationDto & { productName?: string; vehicleTypeId?: string },
  ) {
    const result = await this.cityProductService.paginateCityProducts(
      cityId,
      query.page,
      query.limit,
      query,
    );
    return {
      success: true,
      message: 'City products retrieved successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }

  @Post('list')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get city products by multiple city IDs' })
  @ApiResponse({ status: 200, type: CityProductListApiResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  async listCityProductsByCityIds(@Body() body: ListCityProductsDto) {
    const data = await this.cityProductService.getCityProductsByCityIds(
      body.cityIds,
    );
    return {
      success: true,
      message: 'City products retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }
}
