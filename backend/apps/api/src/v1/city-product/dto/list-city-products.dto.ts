import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayNotEmpty } from 'class-validator';

export class ListCityProductsDto {
  @ApiProperty({
    description: 'Array of city IDs to get city products for',
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  cityIds!: string[];
}
