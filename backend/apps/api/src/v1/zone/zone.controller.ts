import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import {
  ZonePaginationDto,
  MultipleCitiesZonesDto,
} from './dto/zone-pagination.dto';
import {
  CreateZoneResponseDto,
  ZoneDetailsResponseDto,
  UpdateZoneResponseDto,
  DeleteZoneResponseDto,
  RestoreZoneResponseDto,
  ZonePaginatedResponseDto,
} from './dto/zone-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ZoneService } from '@shared/shared/modules/zone/zone.service';
import {
  ZoneResponseDto,
  ZoneResponseDtoClass,
} from '@shared/shared/modules/zone/zone-response.dto';
import {
  CreateZoneInputDto,
  UpdateZoneInputDto,
} from '@shared/shared/modules/zone/zone-input.dto';

@ApiTags('Zones')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@Controller('zones')
export class ZoneController {
  private readonly logger = new Logger(ZoneController.name);

  constructor(private readonly zoneService: ZoneService) {}

  /**
   * Get all zones with pagination and filtering
   */
  @Get()
  @ApiOperation({
    summary: 'Get all zones',
    description: `Retrieves a paginated list of zones with optional filtering capabilities.
    
    **Features:**
    - Pagination support with customizable page size
    - Search by zone name
    - Filter by city ID
    - Filter by zone type ID
    - Include related data (zone type information)
    - H3 index-based geographic indexing
    
    **Use cases:**
    - List all zones in a city for administrative purposes
    - Search for specific zones by name
    - Browse zones by type (e.g., all airport zones)
    - Get zone data with type information for analysis`,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zones retrieved successfully',
    type: ZonePaginatedResponseDto,
  })
  async findAll(
    @Query() paginationDto: ZonePaginationDto,
  ): Promise<ZonePaginatedResponseDto> {
    this.logger.log(
      `Getting zones: page=${paginationDto.page}, limit=${paginationDto.limit}`,
    );

    const filters: any = {};
    if (paginationDto.search) filters.search = paginationDto.search.trim();
    if (paginationDto.cityId) filters.cityId = paginationDto.cityId;
    if (paginationDto.zoneTypeId) filters.zoneTypeId = paginationDto.zoneTypeId;
    if (paginationDto.includeRelations !== undefined)
      filters.includeRelations = paginationDto.includeRelations;

    const result = await this.zoneService.findPaginated(
      paginationDto.page || 1,
      paginationDto.limit || 10,
      filters,
    );

    return {
      success: true,
      message: 'Zones retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zone by ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get zone by ID',
    description: `Retrieves detailed information about a specific zone.
    
    **Returns:**
    - Complete zone information including polygon geometry
    - H3 indexes for geographic queries
    - Zone type information (if includeRelations=true)
    - Metadata and description
    - Creation and modification timestamps
    
    **Use cases:**
    - Get complete zone details for mapping applications
    - Retrieve zone configuration for ride matching algorithms
    - Fetch zone boundaries for geographic calculations`,
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone retrieved successfully',
    type: ZoneDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone ID format',
    type: ApiErrorResponseDto,
  })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('includeRelations') includeRelations?: boolean,
  ): Promise<ZoneDetailsResponseDto> {
    this.logger.log(`Getting zone by ID: ${id}`);

    const data = await this.zoneService.findById(
      id,
      includeRelations ? { includeRelations } : undefined,
    );

    return {
      success: true,
      message: 'Zone retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zone by name within a city
   */
  @Get('city/:cityId/name/:name')
  @ApiOperation({
    summary: 'Get zone by name within a city',
    description: 'Retrieves a specific zone by its name within a specific city',
  })
  @ApiParam({
    name: 'cityId',
    type: String,
    description: 'City UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'name',
    type: String,
    description: 'Zone name',
    example: 'Downtown Business District',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone retrieved successfully',
    type: ZoneDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone not found',
    type: ApiErrorResponseDto,
  })
  async findByNameInCity(
    @Param('cityId', ParseUUIDPipe) cityId: string,
    @Param('name') name: string,
  ): Promise<ZoneDetailsResponseDto> {
    this.logger.log(`Getting zone by name: ${name} in city: ${cityId}`);

    const data = await this.zoneService.findByName(name, cityId);
    if (!data) {
      throw new Error('Zone not found');
    }

    return {
      success: true,
      message: 'Zone retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zones by city ID
   */
  @Get('city/:cityId')
  @ApiOperation({
    summary: 'Get zones by city',
    description: 'Retrieves all zones within a specific city',
  })
  @ApiParam({
    name: 'cityId',
    type: String,
    description: 'City UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zones retrieved successfully',
    type: [ZoneResponseDtoClass],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid city ID format',
    type: ApiErrorResponseDto,
  })
  async findByCityId(
    @Param('cityId', ParseUUIDPipe) cityId: string,
    @Query('includeRelations') includeRelations?: boolean,
  ): Promise<{
    success: boolean;
    message: string;
    data: ZoneResponseDto[];
    timestamp: number;
  }> {
    this.logger.log(`Getting zones by city ID: ${cityId}`);

    const data = await this.zoneService.findByCityId(
      cityId,
      includeRelations ? { includeRelations } : undefined,
    );

    return {
      success: true,
      message: 'Zones retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zones by multiple city IDs
   */
  @Post('cities/zones')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get zones by multiple cities',
    description: `Retrieves all zones from multiple cities in a single request.

    **Features:**
    - Accepts an array of city IDs
    - Returns zones from all specified cities
    - Includes both city zones (isCityZone: true) and regular zones (isCityZone: false)
    - Optional inclusion of zone type and city details
    - Results are grouped by city and ordered by creation date

    **Use cases:**
    - Get zones from multiple cities for cross-city operations
    - Bulk zone retrieval for administrative dashboards
    - Multi-city zone analysis and reporting`,
  })
  @ApiBody({
    type: MultipleCitiesZonesDto,
    description: 'Array of city IDs to get zones from',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zones retrieved successfully',
    type: [ZoneResponseDtoClass],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  async findByMultipleCityIds(@Body() dto: MultipleCitiesZonesDto): Promise<{
    success: boolean;
    message: string;
    data: ZoneResponseDto[];
    timestamp: number;
  }> {
    this.logger.log(
      `Getting zones by multiple city IDs: ${dto.cityIds.join(', ')}`,
    );

    const data = await this.zoneService.findAllZonesByMultipleCityIds(
      dto.cityIds,
      dto.includeRelations
        ? { includeRelations: dto.includeRelations }
        : undefined,
    );

    return {
      success: true,
      message: 'Zones retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zones by zone type ID
   */
  @Get('zone-type/:zoneTypeId')
  @ApiOperation({
    summary: 'Get zones by zone type',
    description: 'Retrieves all zones of a specific type',
  })
  @ApiParam({
    name: 'zoneTypeId',
    type: String,
    description: 'Zone type UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zones retrieved successfully',
    type: [ZoneResponseDtoClass],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone type ID format',
    type: ApiErrorResponseDto,
  })
  async findByZoneTypeId(
    @Param('zoneTypeId', ParseUUIDPipe) zoneTypeId: string,
  ): Promise<{
    success: boolean;
    message: string;
    data: ZoneResponseDto[];
    timestamp: number;
  }> {
    this.logger.log(`Getting zones by zone type ID: ${zoneTypeId}`);

    const data = await this.zoneService.findByZoneTypeId(zoneTypeId);

    return {
      success: true,
      message: 'Zones retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zones by H3 index
   */
  @Get('h3/:h3Index')
  @ApiOperation({
    summary: 'Get zones by H3 index',
    description: 'Retrieves all zones that contain a specific H3 index',
  })
  @ApiParam({
    name: 'h3Index',
    type: String,
    description: 'H3 index string',
    example: '8a2a1072b59ffff',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zones retrieved successfully',
    type: [ZoneResponseDtoClass],
  })
  async findByH3Index(@Param('h3Index') h3Index: string): Promise<{
    success: boolean;
    message: string;
    data: ZoneResponseDto[];
    timestamp: number;
  }> {
    this.logger.log(`Getting zones by H3 index: ${h3Index}`);

    const data = await this.zoneService.findByH3Index(h3Index);

    return {
      success: true,
      message: 'Zones retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Check if zone name exists in city
   */
  @Get('check-name/:cityId/:name')
  @ApiOperation({
    summary: 'Check if zone name exists',
    description: 'Checks if a zone name already exists within a specific city',
  })
  @ApiParam({
    name: 'cityId',
    type: String,
    description: 'City UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'name',
    type: String,
    description: 'Zone name to check',
    example: 'Downtown Business District',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Name availability checked',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Name availability checked' },
        data: {
          type: 'object',
          properties: {
            exists: { type: 'boolean', example: false },
            available: { type: 'boolean', example: true },
          },
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  async checkNameExists(
    @Param('cityId', ParseUUIDPipe) cityId: string,
    @Param('name') name: string,
  ): Promise<{
    success: boolean;
    message: string;
    data: { exists: boolean; available: boolean };
    timestamp: number;
  }> {
    this.logger.log(`Checking if zone name exists: ${name} in city: ${cityId}`);

    const exists = await this.zoneService.checkNameExists(name, cityId);

    return {
      success: true,
      message: 'Name availability checked',
      data: {
        exists,
        available: !exists,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * Create a new zone
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new zone',
    description: `Creates a new geographic zone with automatic H3 index generation.
    
    **Process:**
    1. Validates the provided GeoJSON polygon
    2. Checks for duplicate zone names within the same city
    3. Generates H3 indexes at resolution 9 for efficient geographic queries
    4. Creates zone-to-H3-index mappings for fast lookups
    5. Stores zone metadata and configuration
    
    **Features:**
    - Automatic H3 index generation for geographic queries
    - GeoJSON polygon validation
    - Duplicate name checking within city scope
    - Optional metadata and description support
    - Zone type assignment for algorithm configuration
    
    **Use cases:**
    - Define service areas for ride-hailing operations
    - Create geographic boundaries for pricing zones
    - Establish operational zones for driver management`,
  })
  @ApiBody({
    type: CreateZoneInputDto,
    description: 'Zone data',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Zone created successfully',
    type: CreateZoneResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Zone with the same name already exists in this city',
    type: ApiErrorResponseDto,
  })
  async create(
    @Body() createDto: CreateZoneInputDto,
  ): Promise<CreateZoneResponseDto> {
    this.logger.log(
      `Creating zone: ${createDto.name} in city: ${createDto.cityId}`,
    );

    const data = await this.zoneService.create(createDto);

    return {
      success: true,
      message: 'Zone created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Update zone by ID
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Update zone',
    description:
      'Updates an existing zone with the provided data. Updates H3 indexes if polygon changes.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: UpdateZoneInputDto,
    description: 'Zone update data',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone updated successfully',
    type: UpdateZoneResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or zone ID',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Zone with the same name already exists in this city',
    type: ApiErrorResponseDto,
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateZoneInputDto,
  ): Promise<UpdateZoneResponseDto> {
    this.logger.log(`Updating zone: ${id}`);

    const data = await this.zoneService.update(id, updateDto);

    return {
      success: true,
      message: 'Zone updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Delete zone by ID (soft delete)
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete zone',
    description:
      'Soft deletes a zone by marking it as deleted and removes H3 index mappings',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone deleted successfully',
    type: DeleteZoneResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone ID format',
    type: ApiErrorResponseDto,
  })
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<DeleteZoneResponseDto> {
    this.logger.log(`Deleting zone: ${id}`);

    const data = await this.zoneService.softDelete(id);

    return {
      success: true,
      message: 'Zone deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Restore soft-deleted zone
   */
  @Put(':id/restore')
  @ApiOperation({
    summary: 'Restore zone',
    description: 'Restores a soft-deleted zone and recreates H3 index mappings',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone restored successfully',
    type: RestoreZoneResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone ID format',
    type: ApiErrorResponseDto,
  })
  async restore(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<RestoreZoneResponseDto> {
    this.logger.log(`Restoring zone: ${id}`);

    const data = await this.zoneService.restore(id);

    return {
      success: true,
      message: 'Zone restored successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Hard delete zone (permanent deletion - use with caution)
   */
  @Delete(':id/hard')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hard delete zone',
    description:
      'Permanently deletes a zone and all associated H3 index mappings. This action cannot be undone.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone permanently deleted',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Zone permanently deleted' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone ID format',
    type: ApiErrorResponseDto,
  })
  async hardDelete(@Param('id', ParseUUIDPipe) id: string): Promise<{
    success: boolean;
    message: string;
    timestamp: number;
  }> {
    this.logger.log(`Hard deleting zone: ${id}`);

    await this.zoneService.hardDelete(id);

    return {
      success: true,
      message: 'Zone permanently deleted',
      timestamp: Date.now(),
    };
  }
}
