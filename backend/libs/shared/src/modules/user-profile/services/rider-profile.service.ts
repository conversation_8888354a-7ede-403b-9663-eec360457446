import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { UserRepository } from '@shared/shared/repositories/user.repository';
import { RoleRepository } from '@shared/shared/repositories/role.repository';
import { UserProfile } from '@shared/shared/repositories/models/userProfile.model';
import { User } from '@shared/shared/repositories/models/user.model';

export interface RiderFilterOptions {
  search?: string;
  name?: string;
  email?: string;
  phoneNumber?: string;
  cityId?: string;
  status?: string;
  riderId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedRiderResult {
  data: (UserProfile & {
    user: User;
    city?: { id: string; name: string; code: string } | null;
    fullName: string;
  })[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface UpdateRiderData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  gender?: string;
  dob?: string;
  cityId?: string;
  profilePictureUrl?: string;
  status?: string;
}

@Injectable()
export class RiderProfileService {
  private readonly logger = new Logger(RiderProfileService.name);

  constructor(
    private readonly userProfileRepository: UserProfileRepository,
    private readonly userRepository: UserRepository,
    private readonly roleRepository: RoleRepository,
  ) { }

  /**
   * Get paginated list of riders with filters
   */
  async listRiders(filters: RiderFilterOptions): Promise<PaginatedRiderResult> {
    const {
      search,
      name,
      email,
      phoneNumber,
      status,
      riderId,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = filters;

    this.logger.log(`Fetching riders with filters: ${JSON.stringify(filters)}`);

    // Get rider role
    const riderRole = await this.roleRepository.findByIdentifier('rider');
    if (!riderRole) {
      throw new NotFoundException('Rider role not found');
    }

    // Build where conditions
    const whereConditions: any = {
      roleId: riderRole.id,
      deletedAt: null,
    };

    // Add status filter
    if (status) {
      whereConditions.status = status;
    }

    // Add riderId filter
    if (riderId) {
      whereConditions.id = riderId;
    }

    // Build user conditions for search
    const userConditions: any = {};

    if (email) {
      userConditions.email = {
        contains: email,
        mode: 'insensitive',
      };
    }

    if (phoneNumber) {
      userConditions.phoneNumber = {
        contains: phoneNumber,
      };
    }

    // Handle search across multiple fields
    if (search) {
      const searchConditions = [];

      // Search in profile fields
      searchConditions.push({
        firstName: { contains: search, mode: 'insensitive' },
      });
      searchConditions.push({
        lastName: { contains: search, mode: 'insensitive' },
      });

      // Search in user fields
      searchConditions.push({
        user: {
          email: { contains: search, mode: 'insensitive' },
        },
      });
      searchConditions.push({
        user: {
          phoneNumber: { contains: search },
        },
      });

      whereConditions.OR = searchConditions;
    } else {
      // Handle individual name search
      if (name) {
        whereConditions.OR = [
          { firstName: { contains: name, mode: 'insensitive' } },
          { lastName: { contains: name, mode: 'insensitive' } },
        ];
      }

      // Add user conditions if any
      if (Object.keys(userConditions).length > 0) {
        whereConditions.user = userConditions;
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await this.userProfileRepository.count(whereConditions);

    // Get riders with relations
    const riders = await this.userProfileRepository.findMany({
      where: whereConditions,
      include: {
        user: {
          select: {
            id: true,
            phoneNumber: true,
            email: true,
            phoneVerifiedAt: true,
            emailVerifiedAt: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
      orderBy: {
        [sortBy]: sortOrder.toLowerCase(),
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    this.logger.log(
      `Found ${total} riders, returning page ${page} of ${totalPages}`,
    );

    return {
      data: riders as any,
      meta: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  /**
   * Get rider by ID
   */
  async getRiderById(riderId: string): Promise<UserProfile> {
    this.logger.log(`Fetching rider with ID: ${riderId}`);

    // Get rider role
    const riderRole = await this.roleRepository.findByIdentifier('rider');
    if (!riderRole) {
      throw new NotFoundException('Rider role not found');
    }

    const rider = await this.userProfileRepository.findUnique({
      where: {
        id: riderId,
        roleId: riderRole.id,
        deletedAt: null,
      },
      include: {
        user: {
          select: {
            id: true,
            phoneNumber: true,
            email: true,
            phoneVerifiedAt: true,
            emailVerifiedAt: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    if (!rider) {
      throw new NotFoundException(`Rider with ID ${riderId} not found`);
    }

    this.logger.log(`Found rider: ${rider.firstName} ${rider.lastName}`);

    return rider as any;
  }

  /**
   * Update rider profile
   */
  async updateRider(
    riderId: string,
    updateData: UpdateRiderData,
  ): Promise<UserProfile> {
    this.logger.log(`Updating rider with ID: ${riderId}`);

    // Get rider role
    const riderRole = await this.roleRepository.findByIdentifier('rider');
    if (!riderRole) {
      throw new NotFoundException('Rider role not found');
    }

    // Check if rider exists
    const existingRider = await this.userProfileRepository.findUnique({
      where: {
        id: riderId,
        roleId: riderRole.id,
        deletedAt: null,
      },
      include: {
        user: true,
      },
    });

    if (!existingRider) {
      throw new NotFoundException(`Rider with ID ${riderId} not found`);
    }

    // Prepare profile update data
    const profileUpdateData: any = {};
    if (updateData.firstName !== undefined)
      profileUpdateData.firstName = updateData.firstName;
    if (updateData.lastName !== undefined)
      profileUpdateData.lastName = updateData.lastName;
    if (updateData.gender !== undefined)
      profileUpdateData.gender = updateData.gender;
    if (updateData.dob !== undefined)
      profileUpdateData.dob = updateData.dob ? new Date(updateData.dob) : null;
    if (updateData.cityId !== undefined)
      profileUpdateData.cityId = updateData.cityId;
    if (updateData.profilePictureUrl !== undefined)
      profileUpdateData.profilePictureUrl = updateData.profilePictureUrl;
    if (updateData.status !== undefined)
      profileUpdateData.status = updateData.status;

    // Prepare user update data
    const userUpdateData: any = {};
    if (updateData.email !== undefined) userUpdateData.email = updateData.email;
    if (updateData.phoneNumber !== undefined)
      userUpdateData.phoneNumber = updateData.phoneNumber;

    // Update user data if needed
    if (Object.keys(userUpdateData).length > 0) {
      // Check for email uniqueness if email is being updated
      if (
        updateData.email &&
        existingRider.user &&
        updateData.email !== existingRider.user.email
      ) {
        const existingUserWithEmail = await this.userRepository.findUnique({
          where: { email: updateData.email },
        });
        if (
          existingUserWithEmail &&
          existingUserWithEmail.id !== existingRider.userId
        ) {
          throw new BadRequestException(
            `Email ${updateData.email} is already in use`,
          );
        }
      }

      // Check for phone number uniqueness if phone is being updated
      if (
        updateData.phoneNumber &&
        existingRider.user &&
        updateData.phoneNumber !== existingRider.user.phoneNumber
      ) {
        const existingUserWithPhone = await this.userRepository.findUnique({
          where: { phoneNumber: updateData.phoneNumber },
        });
        if (
          existingUserWithPhone &&
          existingUserWithPhone.id !== existingRider.userId
        ) {
          throw new BadRequestException(
            `Phone number ${updateData.phoneNumber} is already in use`,
          );
        }
      }

      await this.userRepository.update({
        where: { id: existingRider.userId },
        data: userUpdateData,
      });
    }

    // Update profile data if needed
    if (Object.keys(profileUpdateData).length > 0) {
      await this.userProfileRepository.update({
        where: { id: riderId },
        data: profileUpdateData,
      });
    }

    this.logger.log(`Successfully updated rider: ${riderId}`);

    // Return updated rider
    return this.getRiderById(riderId);
  }
}
