import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Coupon, CouponApplicabilityData } from './models/coupon.model';
import { PrismaService } from '../database/prisma/prisma.service';
import { ApplicabilityType, DiscountType } from '@prisma/client';

export interface CouponFilterOptions {
  search?: string | undefined;
  applicabilityType?: ApplicabilityType | undefined;
  discountType?: DiscountType | undefined;
  isActive?: boolean | undefined;
  cityId?: string | undefined;
  productId?: string | undefined;
  isExpired?: boolean | undefined;
  page?: number | undefined;
  limit?: number | undefined;
  sortBy?: string | undefined;
  sortOrder?: 'asc' | 'desc' | undefined;
}

export interface CouponListResult {
  data: Coupon[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

@Injectable()
export class CouponRepository extends BaseRepository<Coupon> {
  protected readonly modelName = 'coupon';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new coupon with applicability rules
   */
  async createCoupon(
    couponData: Omit<
      Coupon,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'usageCount'
    >,
    applicabilityData: CouponApplicabilityData,
  ): Promise<Coupon> {
    return this.prisma.$transaction(async (tx) => {
      // Create the coupon
      const coupon = await tx.coupon.create({
        data: {
          name: couponData.name,
          code: couponData.code,
          thumbnail: couponData.thumbnail ?? null,
          description: couponData.description ?? null,
          startDate: couponData.startDate,
          endDate: couponData.endDate,
          discountType: couponData.discountType,
          discountValue: couponData.discountValue,
          maxDiscountLimit: couponData.maxDiscountLimit ?? null,
          minFareCondition: couponData.minFareCondition ?? null,
          usageLimit: couponData.usageLimit,
          usageCount: 0,
          applicabilityType: couponData.applicabilityType,
          applyConditionLogic: couponData.applyConditionLogic ?? null,
          isActive: couponData.isActive,
          metadata: couponData.metadata,
        },
        include: this.getIncludeOptions(),
      });

      // Create applicability relations based on type
      await this.createApplicabilityRelations(tx, coupon.id, applicabilityData);

      // Fetch the complete coupon with relations within the transaction
      const result = await tx.coupon.findFirst({
        where: { id: coupon.id, deletedAt: null },
        include: this.getIncludeOptions(),
      });

      if (!result) {
        throw new Error('Failed to create coupon');
      }

      return this.mapToModel(result);
    });
  }

  /**
   * Update coupon with applicability rules
   */
  async updateCoupon(
    id: string,
    couponData: Partial<Coupon>,
    applicabilityData?: CouponApplicabilityData,
  ): Promise<Coupon | null> {
    return this.prisma.$transaction(async (tx) => {
      // Prepare update data excluding relation fields
      const updateData: any = {};
      if (couponData.name !== undefined) updateData.name = couponData.name;
      if (couponData.code !== undefined) updateData.code = couponData.code;
      if (couponData.thumbnail !== undefined)
        updateData.thumbnail = couponData.thumbnail ?? null;
      if (couponData.description !== undefined)
        updateData.description = couponData.description ?? null;
      if (couponData.startDate !== undefined)
        updateData.startDate = couponData.startDate;
      if (couponData.endDate !== undefined)
        updateData.endDate = couponData.endDate;
      if (couponData.discountType !== undefined)
        updateData.discountType = couponData.discountType;
      if (couponData.discountValue !== undefined)
        updateData.discountValue = couponData.discountValue;
      if (couponData.maxDiscountLimit !== undefined)
        updateData.maxDiscountLimit = couponData.maxDiscountLimit ?? null;
      if (couponData.minFareCondition !== undefined)
        updateData.minFareCondition = couponData.minFareCondition ?? null;
      if (couponData.usageLimit !== undefined)
        updateData.usageLimit = couponData.usageLimit;
      if (couponData.applicabilityType !== undefined)
        updateData.applicabilityType = couponData.applicabilityType;
      if (couponData.applyConditionLogic !== undefined)
        updateData.applyConditionLogic = couponData.applyConditionLogic ?? null;
      if (couponData.isActive !== undefined)
        updateData.isActive = couponData.isActive;

      if (couponData.metadata !== undefined)
        updateData.metadata = couponData.metadata;
      // Update the coupon
      await tx.coupon.update({
        where: { id, deletedAt: null },
        data: updateData,
      });

      // Update applicability relations if provided
      if (applicabilityData) {
        // Delete existing relations
        await this.deleteApplicabilityRelations(tx, id);
        // Create new relations
        await this.createApplicabilityRelations(tx, id, applicabilityData);
      }

      // Fetch the complete coupon with relations within the transaction
      const result = await tx.coupon.findFirst({
        where: { id, deletedAt: null },
        include: this.getIncludeOptions(),
      });

      return result ? this.mapToModel(result) : null;
    });
  }

  /**
   * Find coupon by ID with all relations
   */
  async findCouponById(id: string): Promise<Coupon | null> {
    const coupon = await this.prisma.coupon.findFirst({
      where: { id, deletedAt: null },
      include: this.getIncludeOptions(),
    });

    return coupon ? this.mapToModel(coupon) : null;
  }

  /**
   * Find coupon by code (case-insensitive)
   */
  async findCouponByCode(code: string): Promise<Coupon | null> {
    const coupon = await this.prisma.coupon.findFirst({
      where: {
        code: {
          equals: code,
          mode: 'insensitive',
        },
        deletedAt: null,
      },
      include: this.getIncludeOptions(),
    });

    return coupon ? this.mapToModel(coupon) : null;
  }

  /**
   * List coupons with filtering and pagination
   */
  async listCoupons(options: CouponFilterOptions): Promise<CouponListResult> {
    const {
      search,
      applicabilityType,
      discountType,
      isActive,
      cityId,
      productId,
      isExpired,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = options;

    const skip = (page - 1) * limit;
    const now = new Date();

    // Build where clause
    const where: any = {
      deletedAt: null,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(applicabilityType && { applicabilityType }),
      ...(discountType && { discountType }),
      ...(isActive !== undefined && { isActive }),
      ...(isExpired !== undefined && {
        ...(isExpired ? { endDate: { lt: now } } : { endDate: { gte: now } }),
      }),
    };

    // Add city/product filters if provided
    if (cityId || productId) {
      const relationFilters: any[] = [];

      if (cityId && applicabilityType === ApplicabilityType.CITY) {
        relationFilters.push({
          couponZones: {
            some: {
              zone: { cityId },
            },
          },
        });
      }

      if (productId && applicabilityType === ApplicabilityType.PRODUCT) {
        relationFilters.push({
          couponProducts: {
            some: { productId },
          },
        });
      }

      if (relationFilters.length > 0) {
        where.AND = relationFilters;
      }
    }

    // Execute queries
    const [coupons, total] = await Promise.all([
      this.prisma.coupon.findMany({
        where,
        include: this.getIncludeOptions(),
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.coupon.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: coupons.map((coupon) => this.mapToModel(coupon)),
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Check if coupon code exists (case-insensitive)
   */
  async codeExists(code: string, excludeId?: string): Promise<boolean> {
    const where: any = {
      code: {
        equals: code,
        mode: 'insensitive',
      },
      deletedAt: null,
    };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const count = await this.prisma.coupon.count({ where });
    return count > 0;
  }

  /**
   * Get expired coupons
   */
  async getExpiredCoupons(): Promise<Coupon[]> {
    const now = new Date();
    const coupons = await this.prisma.coupon.findMany({
      where: {
        endDate: { lt: now },
        isActive: true,
        deletedAt: null,
      },
      include: this.getIncludeOptions(),
    });

    return coupons.map((coupon) => this.mapToModel(coupon));
  }

  /**
   * Soft delete coupon
   */
  async softDeleteCoupon(id: string): Promise<Coupon | null> {
    try {
      const coupon = await this.prisma.coupon.update({
        where: { id, deletedAt: null },
        data: {
          deletedAt: new Date(),
          isActive: false,
        },
        include: this.getIncludeOptions(),
      });

      return this.mapToModel(coupon);
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null;
      }
      throw error;
    }
  }

  /**
   * Toggle coupon active status
   */
  async toggleActiveStatus(id: string): Promise<Coupon | null> {
    const coupon = await this.prisma.coupon.findFirst({
      where: { id, deletedAt: null },
    });

    if (!coupon) {
      return null;
    }

    const updated = await this.prisma.coupon.update({
      where: { id },
      data: {
        isActive: !coupon.isActive,
      },
      include: this.getIncludeOptions(),
    });

    return this.mapToModel(updated);
  }

  /**
   * Private helper methods
   */
  private getIncludeOptions() {
    return {
      couponZones: {
        include: {
          zone: {
            include: {
              city: true,
            },
          },
        },
      },
      couponProducts: {
        include: {
          product: true,
        },
      },
      couponCityProducts: {
        include: {
          cityProduct: {
            include: {
              city: true,
              product: true,
            },
          },
        },
      },
      couponUsers: {
        include: {
          userProfile: {
            include: {
              user: true,
            },
          },
        },
      },
    };
  }

  private async createApplicabilityRelations(
    tx: any,
    couponId: string,
    applicabilityData: CouponApplicabilityData,
  ): Promise<void> {
    const { type, zoneIds, productIds, cityProductIds, userIds } =
      applicabilityData;

    try {
      switch (type) {
        case ApplicabilityType.CITY:
          if (zoneIds && zoneIds.length > 0) {
            // Validate zone IDs exist
            const validZones = await tx.zone.findMany({
              where: { id: { in: zoneIds }, deletedAt: null },
              select: { id: true },
            });

            if (validZones.length !== zoneIds.length) {
              const validZoneIds = validZones.map((z: any) => z.id);
              const invalidIds = zoneIds.filter(
                (id) => !validZoneIds.includes(id),
              );
              throw new Error(`Invalid zone IDs: ${invalidIds.join(', ')}`);
            }

            await tx.couponZone.createMany({
              data: zoneIds.map((zoneId) => ({
                couponId,
                zoneId,
              })),
            });
          }
          break;

        case ApplicabilityType.PRODUCT:
          if (productIds && productIds.length > 0) {
            // Validate product IDs exist
            const validProducts = await tx.product.findMany({
              where: { id: { in: productIds }, deletedAt: null },
              select: { id: true },
            });

            if (validProducts.length !== productIds.length) {
              const validProductIds = validProducts.map((p: any) => p.id);
              const invalidIds = productIds.filter(
                (id) => !validProductIds.includes(id),
              );
              throw new Error(`Invalid product IDs: ${invalidIds.join(', ')}`);
            }

            await tx.couponProduct.createMany({
              data: productIds.map((productId) => ({
                couponId,
                productId,
              })),
            });
          }
          break;

        case ApplicabilityType.CITY_PRODUCT:
          if (cityProductIds && cityProductIds.length > 0) {
            // Validate city product IDs exist
            const validCityProducts = await tx.cityProduct.findMany({
              where: { id: { in: cityProductIds }, deletedAt: null },
              select: { id: true },
            });

            if (validCityProducts.length !== cityProductIds.length) {
              const validCityProductIds = validCityProducts.map(
                (cp: any) => cp.id,
              );
              const invalidIds = cityProductIds.filter(
                (id) => !validCityProductIds.includes(id),
              );
              throw new Error(
                `Invalid city product IDs: ${invalidIds.join(', ')}`,
              );
            }

            await tx.couponCityProduct.createMany({
              data: cityProductIds.map((cityProductId) => ({
                couponId,
                cityProductId,
              })),
            });
          }
          break;

        case ApplicabilityType.USER:
          if (userIds && userIds.length > 0) {
            // Validate user IDs exist
            const validUsers = await tx.userProfile.findMany({
              where: { id: { in: userIds }, deletedAt: null },
              select: { id: true },
            });

            if (validUsers.length !== userIds.length) {
              const validUserIds = validUsers.map((u: any) => u.id);
              const invalidIds = userIds.filter(
                (id) => !validUserIds.includes(id),
              );
              throw new Error(`Invalid user IDs: ${invalidIds.join(', ')}`);
            }

            await tx.couponUser.createMany({
              data: userIds.map((userId) => ({
                couponId,
                userId,
              })),
            });
          }
          break;
      }
    } catch (error: any) {
      console.error('Error creating applicability relations:', error);
      throw new Error(
        `Failed to create applicability relations: ${error.message}`,
      );
    }
  }

  /**
   * Increment coupon usage count
   */
  async incrementUsageCount(couponId: string): Promise<Coupon | null> {
    const result = await this.prisma.coupon.update({
      where: { id: couponId },
      data: {
        usageCount: {
          increment: 1,
        },
      },
      include: this.getIncludeOptions(),
    });

    return result ? this.mapToModel(result) : null;
  }

  /**
   * Decrement coupon usage count
   */
  async decrementUsageCount(couponId: string): Promise<Coupon | null> {
    // First check current usage count to avoid negative values
    const current = await this.prisma.coupon.findUnique({
      where: { id: couponId },
      select: { usageCount: true },
    });

    if (!current || current.usageCount <= 0) {
      return null;
    }

    const result = await this.prisma.coupon.update({
      where: { id: couponId },
      data: {
        usageCount: {
          decrement: 1,
        },
      },
      include: this.getIncludeOptions(),
    });

    return result ? this.mapToModel(result) : null;
  }

  /**
   * Find applicable coupons for user's location with database-level filtering
   */
  async findApplicableCouponsForUser(options: {
    userId: string;
    zoneIds: string[]; // Changed to accept multiple zone IDs
    cityId: string;
    productIds: string[];
    cityProductIds: string[];
    page: number;
    limit: number;
  }): Promise<{
    data: Coupon[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }> {
    const { userId, zoneIds, productIds, cityProductIds, page, limit } =
      options;
    const skip = (page - 1) * limit;
    const now = new Date();

    // Build the where clause with OR conditions for different applicability types
    const whereClause = {
      isActive: true,
      endDate: { gte: now },
      deletedAt: null,
      OR: [
        // CITY applicability: coupon zones contain any of the current zones
        {
          applicabilityType: ApplicabilityType.CITY,
          couponZones: {
            some: {
              zoneId: { in: zoneIds }, // Check if any coupon zone matches any of the user's zones
            },
          },
        },
        // PRODUCT applicability: coupon products match city's available products
        {
          applicabilityType: ApplicabilityType.PRODUCT,
          couponProducts: {
            some: {
              productId: { in: productIds },
            },
          },
        },
        // CITY_PRODUCT applicability: coupon city-products match current city-products
        {
          applicabilityType: ApplicabilityType.CITY_PRODUCT,
          couponCityProducts: {
            some: {
              cityProductId: { in: cityProductIds },
            },
          },
        },
        // USER applicability: user is in coupon's applicable users
        {
          applicabilityType: ApplicabilityType.USER,
          couponUsers: {
            some: {
              userId: userId,
            },
          },
        },
      ],
    };

    // Execute queries in parallel
    const [coupons, total] = await Promise.all([
      this.prisma.coupon.findMany({
        where: whereClause,
        include: this.getIncludeOptions(),
        skip,
        take: limit,
        orderBy: [
          { endDate: 'asc' }, // Expiring soon first
          { createdAt: 'desc' }, // Newest first
        ],
      }),
      this.prisma.coupon.count({ where: whereClause }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: coupons.map((coupon) => this.mapToModel(coupon)),
      meta: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  private async deleteApplicabilityRelations(
    tx: any,
    couponId: string,
  ): Promise<void> {
    await Promise.all([
      tx.couponZone.deleteMany({ where: { couponId } }),
      tx.couponProduct.deleteMany({ where: { couponId } }),
      tx.couponCityProduct.deleteMany({ where: { couponId } }),
      tx.couponUser.deleteMany({ where: { couponId } }),
    ]);
  }

  private mapToModel(prismaResult: any): Coupon {
    return {
      id: prismaResult.id,
      name: prismaResult.name,
      code: prismaResult.code,
      thumbnail: prismaResult.thumbnail,
      description: prismaResult.description,
      startDate: prismaResult.startDate,
      endDate: prismaResult.endDate,
      discountType: prismaResult.discountType,
      discountValue: Number(prismaResult.discountValue),
      maxDiscountLimit: prismaResult.maxDiscountLimit
        ? Number(prismaResult.maxDiscountLimit)
        : null,
      minFareCondition: prismaResult.minFareCondition
        ? Number(prismaResult.minFareCondition)
        : null,
      usageLimit: prismaResult.usageLimit,
      usageCount: prismaResult.usageCount,
      applicabilityType: prismaResult.applicabilityType,
      applyConditionLogic: prismaResult.applyConditionLogic,
      metadata: prismaResult.metadata,
      isActive: prismaResult.isActive,
      createdAt: prismaResult.createdAt,
      updatedAt: prismaResult.updatedAt,
      deletedAt: prismaResult.deletedAt,
      couponZones: prismaResult.couponZones,
      couponProducts: prismaResult.couponProducts,
      couponCityProducts: prismaResult.couponCityProducts,
      couponUsers: prismaResult.couponUsers,
    };
  }
}
